@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* 重庆银行品牌色彩系统 - 苹果风格优化 */
    --primary-color: #007AFF;
    --primary-hover: #0056CC;
    --accent-color: #FF9500;
    --accent-hover: #FF8C00;
    --success-color: #34C759;
    --success-hover: #30B050;
    --warning-color: #FF9500;
    --danger-color: #FF3B30;
    /* 注释：secondary-color、nav-color 变量已移除，因为在当前项目中未被使用 */

    /* 注释：文本颜色和背景色变量已移除，因为在当前项目中未被使用 */

    /* 动画过渡 - 苹果风格缓动 */
    --transition-smooth: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-fast: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-spring: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    /* 圆角系统 - 苹果风格 */
    --border-radius-xs: 6px;
    --border-radius-sm: 10px;
    --border-radius-md: 14px;
    --border-radius-lg: 18px;
    --border-radius-xl: 22px;
    --border-radius-full: 50px;

    /* 阴影系统 - 苹果风格 */
    --shadow-xs: 0 1px 3px rgba(0, 0, 0, 0.12);
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.15);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.18);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.22);
    --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.25);

    /* 保留正在使用的玻璃效果变量 */
    --glass-backdrop: blur(20px) saturate(180%);

    /* 注释：其他玻璃效果变量已移除，因为在当前项目中未被使用 */

    /* shadcn/ui 兼容性变量 */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft JhengHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  }
}

/* 重庆银行AI助手专用样式 */
@layer components {

  /* 注释：pulse 动画已移除，因为在当前项目中未被使用 */

  /* 注释：glass-morphism 样式已移除，因为在当前项目中未被使用 */

  /* 悬停效果 */
  .hover-lift {
    transition: var(--transition-fast);
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  /* 苹果风格按钮系统 */
  .btn-base {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: var(--border-radius-md);
    font-size: 16px;
    font-weight: 600;
    line-height: 1.2;
    text-align: center;
    border: none;
    cursor: pointer;
    transition: var(--transition-fast);
    backdrop-filter: var(--glass-backdrop);
    position: relative;
    overflow: hidden;
  }

  .btn-base:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .btn-base:hover:before {
    left: 100%;
  }

  .btn-base:hover {
    transform: translateY(-2px);
  }

  .btn-base:active {
    transform: translateY(0);
  }

  /* 主要按钮 */
  .btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    box-shadow: var(--shadow-sm);
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-color) 100%);
    box-shadow: var(--shadow-md);
  }

  /* 成功按钮 */
  .btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-hover) 100%);
    color: white;
    box-shadow: var(--shadow-sm);
  }

  .btn-success:hover {
    background: linear-gradient(135deg, var(--success-hover) 0%, var(--success-color) 100%);
    box-shadow: var(--shadow-md);
  }

  /* 次要按钮 - 更新为直接使用颜色值 */
  .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: var(--glass-backdrop);
  }

  .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
  }

  /* 注释：btn-nav、btn-warning、btn-danger 样式已移除，因为在当前项目中未被使用 */

  /* 保留 btn-small 样式，因为在 ChatWindow 中使用 */
  .btn-small {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: var(--border-radius-sm);
  }

  /* 全局滚动条样式 - 始终可见且稳定布局 */
  .scrollable-area {
    /* 现代浏览器：始终为滚动条预留空间 */
    scrollbar-gutter: stable;
    /* Firefox滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.05);
  }

  .scrollable-area::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    /* 始终显示滚动条轨道 */
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }

  .scrollable-area::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }

  .scrollable-area::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    /* 确保滚动条拇指始终可见 */
    min-height: 20px;
  }

  .scrollable-area::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
  }

  .scrollable-area::-webkit-scrollbar-thumb:active {
    background: rgba(0, 0, 0, 0.4);
  }

  /* 关爱模式下的滚动条样式 - 更粗更明显 */
  .care-mode .scrollable-area::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }

  .care-mode .scrollable-area::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.3);
  }

  .care-mode .scrollable-area::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.4);
  }

  .care-mode .scrollable-area {
    scrollbar-width: auto;
    scrollbar-color: rgba(0, 0, 0, 0.3) rgba(0, 0, 0, 0.05);
  }

  /* 注释：btn-large、btn-mini 样式已移除，因为在当前项目中未被使用 */

  /* 响应式断点 */
  @media (min-width: 2560px) {

    /* 4K分辨率 */
    .greeting-page-container {
      font-size: 18px;
    }
  }

  @media (max-width: 2559px) and (min-width: 1920px) {

    /* 2K分辨率 */
    .greeting-page-container {
      font-size: 16px;
    }
  }

  @media (max-width: 1919px) and (min-width: 1600px) {

    /* 1080p分辨率 */
    .greeting-page-container {
      font-size: 14px;
    }

    /* 1080p下的全部业务区域优化 */
    .greeting-page-container .content-area>div:first-child>div:last-child {
      /* 全部业务区域 */
      min-height: 420px;
      /* 适度增加最小高度，让内容更饱满 */
    }

    /* 1080p下的常用功能区域优化 */
    .greeting-page-container .content-area>div:first-child>div:first-child {
      /* 常用功能区域 */
      max-height: 180px;
      /* 适度限制高度，给全部业务区域更多空间 */
    }

    /* 1080p下的聊天区域优化 */
    .greeting-page-container .content-area>div:last-child>div:first-child {
      /* 聊天窗口 */
      min-height: 500px;
    }

    /* 1080p下的智能推荐区域优化 */
    .greeting-page-container .content-area>div:last-child>div:last-child {
      /* 智能推荐区域 */
      max-height: 280px;
    }
  }

  @media (max-width: 1599px) {

    /* 小屏适配 */
    .greeting-page-container {
      font-size: 12px;
      padding: 16px;
    }

    .greeting-page-container .content-area {
      flex-direction: column;
      gap: 16px;
    }
  }
}