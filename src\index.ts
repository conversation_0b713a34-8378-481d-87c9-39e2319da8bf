/**
 * WebSDK主入口文件 - 极简版本
 */

// 导入样式文件
import './styles/globals.css';

// 核心SDK
export { WebSDK, init, getWebSDK } from './core/WebSDK';

// 工具函数
export { Logger, LogLevel } from './utils/Logger';

// 核心类型定义
export type { WebSDKConfig } from './types/config';

// JSON-RPC类型定义
export type {
  JsonRpcRequest,
  JsonRpcResponse,
  JsonRpcNotification,
  JsonRpcMessage,
  JsonRpcMessageOptions,
  SpeakParams,
  UpdateBackgroundInfoParams,
  AddMessagesParams,
  PushBizDataParams,
} from './types/jsonrpc';

// 事件总线
export { EventBus } from './core/EventBus';

// UI组件 - React版本（唯一版本）
export { ChatWidgetReact } from './ui/ChatWidgetReact';
export { ChatWidgetManager } from './ui/ChatWidgetManager';

// 自定义元素组件
import './ui/ChatWidgetWrapper'; // 注册React版本的Web Component为 chat-widget
