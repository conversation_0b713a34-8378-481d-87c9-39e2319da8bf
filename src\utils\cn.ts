/**
 * shadcn/ui 样式工具函数
 * 用于合并和处理Tailwind CSS类名
 */

import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * 合并类名的工具函数
 * 这是shadcn/ui的核心工具函数，用于智能合并Tailwind CSS类名
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 条件类名辅助函数
 */
export function conditionalClass(
  condition: boolean,
  trueClass: string,
  falseClass?: string
): string {
  return condition ? trueClass : falseClass || '';
}

/**
 * 主题相关的类名生成器
 */
export function themeClass(isDark: boolean, lightClass: string, darkClass: string): string {
  return isDark ? darkClass : lightClass;
}

/**
 * 变体类名生成器（基于class-variance-authority的简化版本）
 */
export function variants(
  base: string,
  variants: Record<string, string>,
  props: Record<string, unknown>
): string {
  let result = base;

  for (const [key, value] of Object.entries(props)) {
    if (value && variants[`${key}-${value}`]) {
      result = cn(result, variants[`${key}-${value}`]);
    }
  }

  return result;
}

/**
 * 响应式类名生成器
 */
export function responsive(classes: {
  base?: string;
  sm?: string;
  md?: string;
  lg?: string;
  xl?: string;
}): string {
  const classArray: string[] = [];

  if (classes.base) classArray.push(classes.base);
  if (classes.sm) classArray.push(`sm:${classes.sm}`);
  if (classes.md) classArray.push(`md:${classes.md}`);
  if (classes.lg) classArray.push(`lg:${classes.lg}`);
  if (classes.xl) classArray.push(`xl:${classes.xl}`);

  return cn(...classArray);
}

/**
 * 状态类名生成器
 */
export function stateClass(classes: {
  base?: string;
  hover?: string;
  focus?: string;
  active?: string;
  disabled?: string;
}): string {
  const classArray: string[] = [];

  if (classes.base) classArray.push(classes.base);
  if (classes.hover) classArray.push(`hover:${classes.hover}`);
  if (classes.focus) classArray.push(`focus:${classes.focus}`);
  if (classes.active) classArray.push(`active:${classes.active}`);
  if (classes.disabled) classArray.push(`disabled:${classes.disabled}`);

  return cn(...classArray);
}
