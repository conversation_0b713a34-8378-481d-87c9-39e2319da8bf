# 重庆银行AI助手 - 快速上手指南

## 🚀 5分钟快速了解项目

### 项目概述
这是一个基于**原生HTML/CSS/JavaScript**开发的银行AI助手原型，主要特色：
- 🎭 **虚拟数字人交互** - 视频播放 + 语音合成
- 🗣️ **川渝话支持** - 普通话/川渝话双语切换  
- 🎨 **三种主题模式** - 暗色/明亮/关爱模式
- 📱 **响应式设计** - 1080p-4K分辨率适配

### 核心文件（3个）
```
tanqi/
├── bank-virtual-assistant.html  # 主页面（394行）
├── bank-virtual-assistant.css   # 样式文件（7945行）
└── bank-virtual-assistant.js    # 脚本文件（1730行）
```

## 🏗️ 页面结构（一图看懂）

```
┌─────────────────────────────────────────────────────────────┐
│  Header: 标题 + 主题切换 + 语言切换 + 关爱模式              │
├─────────────────┬─────────────────┬─────────────────────────┤
│  业务展示区      │   虚拟数字人     │    右侧面板              │
│                 │                 │  ┌─────────────────────┤
│  • 常用功能      │  • 视频播放      │  │   聊天区域           │
│  • 业务卡片      │  • 状态显示      │  │                     │
│  • 全部功能      │  • 语音动画      │  │   • 消息列表         │
│                 │  • 特色标签      │  │   • 输入框           │
│                 │                 │  ├─────────────────────┤
│                 │                 │  │   智能推荐区         │
│                 │                 │  │   • 推荐卡片         │
└─────────────────┴─────────────────┴─────────────────────────┘
```

## 🎯 关键功能实现

### 1. 虚拟数字人 (核心亮点)
```javascript
// 视频管理器 - 控制数字人状态
class AvatarVideoManager {
    switchToSpeaking() {  // 切换到说话状态
        const video = this.getRandomVideo('speaking');
        this.loadVideo(video);
    }
    
    switchToIdle() {      // 切换到待机状态  
        const video = this.getRandomVideo('idle');
        this.loadVideo(video);
    }
}

// 语音合成 - 数字人开口说话
function speak(text) {
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.onstart = () => avatarVideoManager.switchToSpeaking();
    utterance.onend = () => avatarVideoManager.switchToIdle();
    speechSynthesis.speak(utterance);
}
```

### 2. 主题切换 (三种模式)
```javascript
// 主题切换逻辑
function toggleTheme() {
    document.body.classList.toggle('light-theme');
    localStorage.setItem('theme', isLight ? 'light' : 'dark');
}

function toggleCareMode() {
    document.body.classList.toggle('care-theme');  // 大字体模式
    localStorage.setItem('careMode', isCareMode ? 'enabled' : 'disabled');
}
```

### 3. 语言切换 (川渝话特色)
```javascript
// 川渝话转换示例
const chuanyuResponses = {
    '你好': '你好撒！老高兴给你搞服务了',
    '查询余额': '要得嘛，我来给你查银行卡卡的余额哈',
    '转账': '我可以帮你搞转钱的事情撒'
};

// 语音参数调整
if (currentLanguage === 'chuanyu') {
    utterance.rate = 0.9;    // 语速稍慢
    utterance.pitch = 1.1;   // 音调稍高
}
```

### 4. 语音识别 (模拟实现)
```javascript
// 收音动画 + 模拟识别
function startGifRecording() {
    const gif = audioInputMode === 'lip' ? 'chundon.gif' : 'anjian.gif';
    document.getElementById('audioGif').src = `gif/${gif}`;
    
    // 模拟打字效果
    simulateTyping('我想查询账户余额', 150);
}
```

## 🎨 样式系统 (CSS变量驱动)

### CSS变量设计
```css
:root {
    --primary-color: #03a9f4;      /* 主色调 */
    --accent-color: #ffc837;       /* 强调色 */
    --bg-dark: #0a0e27;           /* 暗色背景 */
    --glass-bg-light: rgba(255, 255, 255, 0.15);  /* 玻璃效果 */
    --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}
```

### 响应式断点
```css
/* 4K分辨率 */
@media (min-width: 2560px) { }

/* 2K分辨率 */  
@media (max-width: 2559px) and (min-width: 1920px) { }

/* 1080p分辨率 */
@media (max-width: 1919px) and (min-width: 1600px) { }
```

## 📊 业务数据结构

### 常用功能配置
```javascript
const quickFunctions = [
    { key: 'electronic-channel', name: '电子渠道签约' },
    { key: 'open-card', name: '开卡' },
    { key: 'account-limit', name: '账户非柜面限额设置' },
    { key: 'happiness-deposit', name: '幸福存开户' }
];
```

### 业务分类数据
```javascript
const businessRecommendations = {
    account: [
        { title: '开卡', desc: '申请新银行卡' },
        { title: '个人信息修改', desc: '更新个人基本信息' }
    ],
    inquiry: [
        { title: '账户查询', desc: '查看账户余额信息' },
        { title: '交易明细查询', desc: '查看交易记录明细' }
    ]
};
```

## 🔧 开发调试技巧

### 1. 本地运行
```bash
# 需要HTTP服务器（语音功能需要HTTPS）
python -m http.server 8000
# 或使用Live Server插件
```

### 2. 关键调试点
```javascript
// 开启调试日志
console.log('虚拟人状态切换:', currentState);
console.log('川渝话转换:', originalText, '->', convertedText);
console.log('主题切换:', document.body.className);
```

### 3. 常见问题排查
- **视频不播放**: 检查视频文件路径和格式
- **语音不工作**: 确保HTTPS环境，检查浏览器权限
- **样式错乱**: 检查CSS类名冲突，确认主题类正确应用
- **响应式问题**: 检查媒体查询断点和flex布局

## 🚀 快速修改指南

### 修改主色调
```css
:root {
    --primary-color: #your-color;    /* 改这里 */
    --accent-color: #your-accent;    /* 和这里 */
}
```

### 添加新业务类型
```javascript
// 1. 在HTML中添加业务卡片
<div class="business-card" onclick="selectBusiness('new-business')">

// 2. 在JS中添加推荐数据
const businessRecommendations = {
    'new-business': [
        { title: '新功能', desc: '新功能描述' }
    ]
};
```

### 修改川渝话内容
```javascript
const chuanyuResponses = {
    '新词汇': '对应的川渝话表达'
};
```

## 📝 注意事项

### 性能优化
- 视频文件建议压缩到合理大小
- 长时间运行注意内存清理
- 避免频繁的DOM操作

### 浏览器兼容
- Chrome 80+ (推荐)
- Firefox 75+
- Safari 13+
- 不支持IE

### 部署要求
- 静态文件服务器
- HTTPS环境（语音功能必需）
- 建议CDN加速静态资源

---

## 🎯 5分钟上手清单

- [ ] 了解项目结构（3个核心文件）
- [ ] 理解页面布局（三列布局）
- [ ] 掌握虚拟数字人实现原理
- [ ] 熟悉主题切换机制
- [ ] 了解川渝话特色功能
- [ ] 掌握CSS变量系统
- [ ] 知道如何调试和修改

**恭喜！你已经掌握了项目的核心要点，可以开始深入开发了！** 🎉

如需详细信息，请参考完整的《重庆银行AI助手技术交接文档.md》。
