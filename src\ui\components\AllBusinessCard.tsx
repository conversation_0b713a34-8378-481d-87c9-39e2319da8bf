import React, { useState } from 'react';

/**
 * 全部业务卡片组件
 * 在关爱模式下显示，提供业务列表弹窗功能
 */

interface AllBusinessCardProps {
  className?: string;
  style?: React.CSSProperties;
  onBusinessSelect?: (business: string) => void;
}

interface BusinessItem {
  id: string;
  name: string;
  icon: string;
  description: string;
}

// 模拟业务列表数据
const businessList: BusinessItem[] = [
  { id: 'account', name: '账户查询', icon: '💳', description: '查看账户余额和交易记录' },
  { id: 'transfer', name: '转账汇款', icon: '💸', description: '向他人转账或汇款' },
  { id: 'payment', name: '缴费服务', icon: '📱', description: '水电费、话费等生活缴费' },
  { id: 'loan', name: '贷款服务', icon: '🏠', description: '申请各类贷款产品' },
  { id: 'investment', name: '理财投资', icon: '📈', description: '购买理财产品和基金' },
  { id: 'insurance', name: '保险服务', icon: '🛡️', description: '购买和管理保险产品' },
  { id: 'credit', name: '信用卡', icon: '💳', description: '信用卡申请和管理' },
  { id: 'deposit', name: '存款服务', icon: '💰', description: '定期存款和活期存款' },
];

export const AllBusinessCard: React.FC<AllBusinessCardProps> = ({
  className,
  style,
  onBusinessSelect,
}) => {
  const [showModal, setShowModal] = useState(false);

  const handleCardClick = () => {
    setShowModal(true);
  };

  const handleBusinessClick = (business: BusinessItem) => {
    onBusinessSelect?.(business.name);
    setShowModal(false);
  };

  const handleModalClose = () => {
    setShowModal(false);
  };

  return (
    <>
      {/* 全部业务卡片 */}
      <div
        className={`all-business-card ${className || ''}`}
        style={{
          backgroundColor: '#ffffff',
          borderRadius: '12px',
          padding: '24px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          border: '2px solid #e5e7eb',
          minHeight: '120px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          ...style,
        }}
        onClick={handleCardClick}
        onMouseEnter={e => {
          e.currentTarget.style.transform = 'translateY(-2px)';
          e.currentTarget.style.boxShadow = '0 8px 20px rgba(0, 0, 0, 0.15)';
        }}
        onMouseLeave={e => {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
        }}
      >
        <div
          style={{
            fontSize: '48px',
            marginBottom: '12px',
          }}
        >
          📋
        </div>
        <div
          style={{
            fontSize: '24px',
            fontWeight: 'bold',
            color: '#1f2937',
            marginBottom: '8px',
          }}
        >
          全部业务
        </div>
        <div
          style={{
            fontSize: '16px',
            color: '#6b7280',
            textAlign: 'center',
          }}
        >
          查看所有可用服务
        </div>
      </div>

      {/* 业务列表弹窗 */}
      {showModal && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 10000,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '20px',
          }}
          onClick={handleModalClose}
        >
          <div
            style={{
              backgroundColor: '#ffffff',
              borderRadius: '16px',
              padding: '32px',
              maxWidth: '800px',
              width: '100%',
              maxHeight: '80vh',
              overflow: 'auto',
              boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2)',
            }}
            onClick={e => e.stopPropagation()}
          >
            {/* 弹窗标题 */}
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '24px',
                borderBottom: '2px solid #e5e7eb',
                paddingBottom: '16px',
              }}
            >
              <h2
                style={{
                  fontSize: '28px',
                  fontWeight: 'bold',
                  color: '#1f2937',
                  margin: 0,
                }}
              >
                全部业务
              </h2>
              <button
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '32px',
                  cursor: 'pointer',
                  color: '#6b7280',
                  padding: '8px',
                  borderRadius: '8px',
                  transition: 'background-color 0.2s',
                }}
                onClick={handleModalClose}
                onMouseEnter={e => {
                  e.currentTarget.style.backgroundColor = '#f3f4f6';
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                ×
              </button>
            </div>

            {/* 业务列表网格 */}
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '16px',
              }}
            >
              {businessList.map(business => (
                <div
                  key={business.id}
                  style={{
                    backgroundColor: '#f9fafb',
                    borderRadius: '12px',
                    padding: '20px',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    border: '2px solid #e5e7eb',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '16px',
                  }}
                  onClick={() => handleBusinessClick(business)}
                  onMouseEnter={e => {
                    e.currentTarget.style.backgroundColor = '#eff6ff';
                    e.currentTarget.style.borderColor = '#3b82f6';
                  }}
                  onMouseLeave={e => {
                    e.currentTarget.style.backgroundColor = '#f9fafb';
                    e.currentTarget.style.borderColor = '#e5e7eb';
                  }}
                >
                  <div style={{ fontSize: '32px' }}>{business.icon}</div>
                  <div style={{ flex: 1 }}>
                    <div
                      style={{
                        fontSize: '20px',
                        fontWeight: 'bold',
                        color: '#1f2937',
                        marginBottom: '4px',
                      }}
                    >
                      {business.name}
                    </div>
                    <div
                      style={{
                        fontSize: '16px',
                        color: '#6b7280',
                      }}
                    >
                      {business.description}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
};
