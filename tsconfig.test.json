{"extends": "./tsconfig.json", "compilerOptions": {"noEmit": true, "skipLibCheck": true, "types": ["vitest/globals", "jsdom", "node"], "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/types/*": ["src/types/*"], "@/core/*": ["src/core/*"], "@/transport/*": ["src/transport/*"], "@/utils/*": ["src/utils/*"], "@tests/*": ["tests/*"]}}, "include": ["src/**/*", "tests/**/*", "vitest.config.ts"], "exclude": ["node_modules", "dist"]}