<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天组件演示</title>
    <style>

    </style>
</head>

<body>


    <!-- 聊天组件容器 -->
    <chat-widget style="display: none;"></chat-widget>

    <!-- 加载SDK -->
    <script src="../dist/web-service-sdk.js"></script>
    <script>
        let sdk = null;

        // 初始化SDK
        WebSDK.init({
            hksttUrl: 'ws://*************:8001',
            // aiServerUrl: 'http://*************:8000',
            aiServerUrl: 'http://*************:8000',
            ttsUrl: 'http://*************:8000',
            debug: true
        }).then(sdkInstance => {
            sdk = sdkInstance;
            window.sdk = sdk;


        }).catch(error => {
            console.error('SDK初始化失败:', error);
        });
    </script>
</body>

</html>