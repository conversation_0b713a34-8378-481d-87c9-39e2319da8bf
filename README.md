# Web Service API SDK

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.3+-blue.svg)](https://www.typescriptlang.org/)
[![Node.js](https://img.shields.io/badge/Node.js-16+-green.svg)](https://nodejs.org/)

一个功能强大的Web服务API包装器SDK，支持双WebSocket连接、HTTP通信、TTS语音合成、STT语音识别和数字人交互的统一接口。

## ✨ 特性

- 🔌 **双协议支持**: WebSocket + HTTP/SSE 混合通信
- 🎵 **TTS语音合成**: 支持普通话和川渝话，HTTP+SSE流式传输
- 🎤 **STT语音识别**: 实时语音转文字功能
- 🤖 **AI对话**: 集成大语言模型对话能力
- 👤 **数字人交互**: 完整的数字人动画状态管理
- 📱 **React组件**: 开箱即用的UI组件
- 🛡️ **TypeScript**: 完整的类型定义和类型安全
- 🔄 **事件驱动**: 基于EventBus的松耦合架构
- 🎯 **轻量级**: 模块化设计，按需加载

## 📦 安装

### 使用 pnpm (推荐)
```bash
pnpm install web-service-api-sdk
```

### 使用 npm
```bash
npm install web-service-api-sdk
```

### 使用 yarn
```bash
yarn add web-service-api-sdk
```

## 🚀 快速开始

### 基础使用

```typescript
import { WebServiceSDK } from 'web-service-api-sdk';

// 初始化SDK
const sdk = new WebServiceSDK({
  // WebSocket连接配置
  wsUrl: 'ws://localhost:8080/ws',
  wsUrl2: 'ws://localhost:8081/ws',
  
  // HTTP服务配置
  httpUrl: 'http://localhost:8080',
  
  // TTS服务配置 (支持WebSocket和HTTP+SSE)
  ttsUrl: 'http://localhost:8080', // HTTP+SSE模式
  // ttsUrl: 'ws://localhost:8080/ws/tts', // WebSocket模式
  
  // STT服务配置
  sttUrl: 'ws://localhost:8080/ws/stt',
  
  // 调试模式
  debug: true
});

// 监听连接状态
sdk.onConnectionChange((status) => {
  console.log('连接状态:', status);
});

// 启动SDK
await sdk.start();
```

### TTS语音合成

```typescript
// 普通话TTS
await sdk.speak('你好，欢迎使用WebSDK！');

// 川渝话TTS
await sdk.speak('你好，欢迎使用WebSDK！', '用四川话说这句话');

// 监听TTS事件
sdk.onTTSStatusChange((status) => {
  console.log('TTS状态:', status);
});
```

### AI对话

```typescript
// 发送消息给AI
const response = await sdk.sendMessage('你好，请介绍一下自己');
console.log('AI回复:', response);

// 监听AI回复
sdk.onAIResponse((message) => {
  console.log('收到AI消息:', message);
});
```

### 数字人交互

```typescript
// 监听数字人状态变化
sdk.onDigitalHumanStateChange((state) => {
  console.log('数字人状态:', state); // idle, speaking, listening
});

// 手动控制数字人状态
sdk.setDigitalHumanState('speaking');
```

## 🎨 React组件

### 聊天组件

```tsx
import { ChatWidget } from 'web-service-api-sdk/react';

function App() {
  return (
    <ChatWidget
      sdkConfig={{
        wsUrl: 'ws://localhost:8080/ws',
        httpUrl: 'http://localhost:8080',
        ttsUrl: 'http://localhost:8080',
      }}
      title="AI助手"
      placeholder="请输入您的问题..."
    />
  );
}
```

### 数字人组件

```tsx
import { DigitalHuman } from 'web-service-api-sdk/react';

function App() {
  return (
    <DigitalHuman
      sdkConfig={{
        wsUrl: 'ws://localhost:8080/ws',
        ttsUrl: 'http://localhost:8080',
      }}
      videoSources={{
        idle: '/videos/idle.mp4',
        speaking: '/videos/speaking.mp4',
        listening: '/videos/listening.mp4'
      }}
      autoPlay={true}
    />
  );
}
```

## ⚙️ 配置说明

### SDK配置选项

```typescript
interface WebSDKConfig {
  // WebSocket连接
  wsUrl?: string;           // 主WebSocket连接
  wsUrl2?: string;          // 辅助WebSocket连接
  
  // HTTP服务
  httpUrl?: string;         // HTTP API基础URL
  
  // TTS服务 (支持两种协议)
  ttsUrl?: string;          // TTS服务URL
  
  // STT服务
  sttUrl?: string;          // STT WebSocket URL
  
  // 通用配置
  debug?: boolean;          // 调试模式
  timeout?: number;         // 请求超时时间
  retryAttempts?: number;   // 重试次数
  
  // 事件配置
  enableAutoReconnect?: boolean;  // 自动重连
  heartbeatInterval?: number;     // 心跳间隔
}
```

### TTS配置详解

```typescript
// HTTP+SSE模式 (推荐)
const config = {
  ttsUrl: 'http://*************:8080',
  // SDK会自动检测为HTTP+SSE协议
};

// WebSocket模式 (兼容)
const config = {
  ttsUrl: 'ws://*************:8000/ws/tts/',
  // SDK会自动检测为WebSocket协议
};

// 强制指定协议
const config = {
  tts: {
    serverUrl: 'http://*************:8080',
    protocol: 'http-sse', // 或 'websocket'
    requestTimeout: 30000,
    maxRetryAttempts: 3,
    seed: 42
  }
};
```

## 📚 API文档

### 核心方法

#### `WebServiceSDK`

```typescript
class WebServiceSDK {
  // 初始化和生命周期
  constructor(config: WebSDKConfig)
  async start(): Promise<void>
  async stop(): Promise<void>
  destroy(): void
  
  // TTS语音合成
  async speak(text: string, instruction?: string): Promise<void>
  stopTTS(): void
  pauseTTS(): void
  resumeTTS(): void
  
  // AI对话
  async sendMessage(message: string): Promise<string>
  
  // 数字人控制
  setDigitalHumanState(state: 'idle' | 'speaking' | 'listening'): void
  
  // 事件监听
  onConnectionChange(callback: (status: ConnectionStatus) => void): void
  onTTSStatusChange(callback: (status: TTSStatus) => void): void
  onAIResponse(callback: (message: string) => void): void
  onDigitalHumanStateChange(callback: (state: string) => void): void
}
```

### 事件系统

SDK基于EventBus实现事件驱动架构：

```typescript
// TTS事件
'tts:status-change'     // TTS状态变化
'tts:play-start'        // 开始播放
'tts:play-end'          // 播放结束
'tts:language-change'   // 语言切换

// AI事件
'ai:chat-response'      // AI回复
'ai:chat-final-response' // AI最终回复

// 数字人事件
'digital-human:state-change' // 状态变化

// 连接事件
'connection:status-change'   // 连接状态变化
```

## 🛠️ 开发指南

### 环境要求

- Node.js >= 16.0.0
- pnpm >= 8.0.0 (推荐)
- TypeScript >= 5.3.0

### 开发环境搭建

```bash
# 克隆项目
git clone http://git.withufuture.com/ai-llm/ai_web_sdk.git
cd ai_web_sdk

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 运行测试
pnpm test

# 构建项目
pnpm build
```

### 项目结构

```
src/
├── core/           # 核心模块
│   ├── EventBus.ts    # 事件总线
│   └── WebSDK.ts      # SDK主类
├── services/       # 服务层
│   ├── AIClient.ts         # AI客户端
│   ├── HttpSSETTSService.ts # HTTP+SSE TTS服务
│   ├── CosyVoiceTTSService.ts # WebSocket TTS服务
│   ├── HKSTTClient.ts      # STT客户端
│   └── DigitalHumanController.ts # 数字人控制器
├── transport/      # 传输层
│   └── TransportManager.ts # 传输管理器
├── ui/            # UI组件
│   ├── ChatWidgetReact.tsx    # 聊天组件
│   └── DigitalHumanReact.tsx  # 数字人组件
└── utils/         # 工具类
    ├── Logger.ts      # 日志工具
    └── AudioUtils.ts  # 音频工具
```

### 代码规范

项目使用以下代码规范：

- **ESLint**: Airbnb TypeScript配置
- **Prettier**: 代码格式化
- **TypeScript**: 严格模式
- **Conventional Commits**: Git提交规范

```bash
# 代码检查
pnpm lint

# 自动修复
pnpm lint:fix

# 格式化代码
pnpm format

# 类型检查
pnpm type-check
```

### 测试

```bash
# 运行所有测试
pnpm test

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 监听模式运行测试
pnpm test:watch

# 运行测试UI
pnpm test:ui
```

## 📖 示例

### 完整示例

```html
<!DOCTYPE html>
<html>
<head>
    <title>WebSDK Demo</title>
    <script src="./dist/web-service-sdk.js"></script>
</head>
<body>
    <div id="app">
        <button id="speak-btn">说话测试</button>
        <button id="chat-btn">AI对话</button>
        <div id="status"></div>
    </div>

    <script>
        // 初始化SDK
        const sdk = new WebServiceSDK({
            wsUrl: 'ws://localhost:8080/ws',
            httpUrl: 'http://localhost:8080',
            ttsUrl: 'http://localhost:8080',
            debug: true
        });

        // 状态显示
        const statusDiv = document.getElementById('status');

        // 监听连接状态
        sdk.onConnectionChange((status) => {
            statusDiv.textContent = `连接状态: ${status}`;
        });

        // TTS测试
        document.getElementById('speak-btn').onclick = async () => {
            await sdk.speak('你好，这是一个TTS测试！');
        };

        // AI对话测试
        document.getElementById('chat-btn').onclick = async () => {
            const response = await sdk.sendMessage('你好');
            alert(`AI回复: ${response}`);
        };

        // 启动SDK
        sdk.start().then(() => {
            console.log('SDK启动成功');
        });
    </script>
</body>
</html>
```

### React集成示例

```tsx
import React, { useEffect, useState } from 'react';
import { WebServiceSDK } from 'web-service-api-sdk';

function App() {
  const [sdk, setSdk] = useState<WebServiceSDK | null>(null);
  const [status, setStatus] = useState('未连接');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const sdkInstance = new WebServiceSDK({
      wsUrl: 'ws://localhost:8080/ws',
      httpUrl: 'http://localhost:8080',
      ttsUrl: 'http://localhost:8080',
      debug: true
    });

    sdkInstance.onConnectionChange(setStatus);
    sdkInstance.start();
    setSdk(sdkInstance);

    return () => {
      sdkInstance.destroy();
    };
  }, []);

  const handleSpeak = async () => {
    if (sdk && message) {
      await sdk.speak(message);
    }
  };

  const handleChat = async () => {
    if (sdk && message) {
      const response = await sdk.sendMessage(message);
      alert(`AI回复: ${response}`);
    }
  };

  return (
    <div>
      <h1>WebSDK React Demo</h1>
      <p>状态: {status}</p>
      <input
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        placeholder="输入消息..."
      />
      <button onClick={handleSpeak}>TTS播放</button>
      <button onClick={handleChat}>AI对话</button>
    </div>
  );
}

export default App;
```

## 🔧 故障排除

### 常见问题

#### 1. TTS播放失败
```
错误: TTS服务连接失败
```
**解决方案:**
- 检查TTS服务器地址是否正确
- 确认服务器支持HTTP+SSE或WebSocket协议
- 查看控制台日志获取详细错误信息

#### 2. WebSocket连接失败
```
错误: WebSocket connection failed
```
**解决方案:**
- 检查WebSocket服务器是否运行
- 确认防火墙设置允许WebSocket连接
- 验证URL格式是否正确 (ws:// 或 wss://)

#### 3. 数字人动画不同步
```
问题: 数字人状态未正确切换
```
**解决方案:**
- 确认TTS事件正确触发
- 检查数字人视频资源是否加载成功
- 验证事件监听器是否正确绑定

### 调试技巧

```typescript
// 启用详细日志
const sdk = new WebServiceSDK({
  debug: true,
  // 其他配置...
});

// 监听所有事件
sdk.eventBus.on('*', (eventName, data) => {
  console.log(`事件: ${eventName}`, data);
});

// 检查连接状态
console.log('连接状态:', sdk.getConnectionStatus());

// 检查TTS状态
console.log('TTS状态:', sdk.getTTSStatus());
```

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. **Fork项目**
2. **创建特性分支** (`git checkout -b feature/amazing-feature`)
3. **提交更改** (`git commit -m 'feat: add amazing feature'`)
4. **推送到分支** (`git push origin feature/amazing-feature`)
5. **创建Pull Request**

### 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建工具或辅助工具的变动
```

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

## 📞 支持

如果您在使用过程中遇到问题，请通过以下方式获取帮助：

- 📧 邮箱: <EMAIL>
- 🐛 问题反馈: [GitLab Issues](http://git.withufuture.com/ai-llm/ai_web_sdk/-/issues)
- 📖 文档: [在线文档](http://git.withufuture.com/ai-llm/ai_web_sdk/-/wikis/home)

---

**WebSDK Team** ❤️ 用心打造
