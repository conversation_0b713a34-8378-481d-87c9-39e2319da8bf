# WebSDK Mock Server

简化的CLI命令行界面，用于模拟WebSDK所需的HKSTT语音识别服务。

## 功能特性

- **HKSTT服务模拟**：模拟人脸检测和语音识别功能
- **JSON-RPC协议**：完全符合协议文档规范
- **交互式CLI界面**：用户友好的数字选择菜单
- **开箱即用**：无需复杂配置，一键启动

## 🛠️ 安装和使用

### 环境要求
- Python 3.9+
- uv (Python包管理器)

### 安装步骤

1. **安装uv** (如果还没有安装):
```bash
# Windows
curl -LsSf https://astral.sh/uv/install.ps1 | powershell

# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh
```

2. **创建虚拟环境并安装依赖**:
```bash
cd python-mock-server
uv venv
uv pip install -e .
```

3. **激活虚拟环境**:
```bash
# Windows
.venv\Scripts\activate

# macOS/Linux
source .venv/bin/activate
```

4. **启动应用**:
```bash
python -m websdk_mock_server.main
```

或者使用安装的命令:
```bash
mock-server
```

## 🎮 使用说明

### GUI界面说明

1. **服务器控制面板**:
   - 显示服务器配置信息
   - 启动/停止服务器按钮
   - 清空日志按钮

2. **服务器状态面板**:
   - 实时显示各服务器运行状态
   - 显示活跃连接数

3. **消息发送控制**:
   - **WebSocket 1 消息**: 发送自定义JSON消息
   - **业务流程测试**: 启动完整的业务流程测试

4. **服务器日志**:
   - 实时显示所有服务器活动
   - 包含时间戳和日志级别

### 服务器端口

- **WebSocket 1 (HKSTT)**: `ws://localhost:8001/ws1`
- **WebSocket 2 (AI服务)**: `ws://localhost:8002/ws2`
- **HTTP Server**: `http://localhost:3002`

### API端点

#### HTTP服务器端点:
- `GET /`: 服务器信息
- `POST /api/text`: 接收文本数据
- `POST /api/workflow`: 业务流程端点
- `GET /api/status`: 服务器状态
- `GET /api/health`: 健康检查

## 🔧 配置

### 环境变量
可以通过环境变量自定义配置:

```bash
export WS1_PORT=8001
export WS2_PORT=8002
export HTTP_PORT=3002
export LOG_LEVEL=INFO
export AUDIO_LANGUAGE=zh
```

### 音频功能
- 默认使用gTTS (Google Text-to-Speech)
- 如果gTTS不可用，会使用模拟音频数据
- 支持中文语音合成

## 🧪 测试

### 与WebSDK集成测试

1. 启动Python Mock Server
2. 在WebSDK演示页面中点击"连接"
3. 使用GUI界面发送各种类型的消息
4. 观察完整的业务流程

### 消息格式示例

**普通消息**:
```json
{
  "type": "message",
  "content": {
    "text": "这是一条测试消息",
    "timestamp": "2024-01-01T12:00:00"
  }
}
```

**弹出窗口命令**:
```json
{
  "type": "popup",
  "id": "popup_123",
  "title": "测试弹出窗口",
  "content": {
    "message": "这是弹出窗口内容"
  }
}
```

## 🐛 故障排除

### 常见问题

1. **端口被占用**:
   - 修改配置中的端口号
   - 或者停止占用端口的其他程序

2. **gTTS安装失败**:
   - 检查网络连接
   - 使用模拟音频模式

3. **GUI界面无法启动**:
   - 确保安装了tkinter
   - 在某些Linux发行版中需要单独安装python3-tk

### 日志调试
- 查看GUI界面中的实时日志
- 调整LOG_LEVEL环境变量获取更详细的日志

## 📝 开发

### 项目结构
```
python-mock-server/
├── src/websdk_mock_server/
│   ├── main.py              # 主GUI应用
│   ├── servers/             # 服务器模块
│   │   ├── websocket_server1.py
│   │   ├── websocket_server2.py
│   │   └── http_server.py
│   └── utils/               # 工具模块
│       ├── config.py
│       └── logger.py
├── pyproject.toml           # 项目配置
└── README.md
```

### 扩展功能
- 可以轻松添加新的API端点
- 支持自定义消息处理逻辑
- 可以集成其他TTS引擎

## 📄 许可证

MIT License
