/**
 * Toast提示组件
 * 用于显示操作反馈信息，保持与现有UI风格一致
 */

import { CheckCircle, AlertCircle, X } from 'lucide-react';
import React, { useEffect, useState, useCallback } from 'react';

export interface ToastProps {
  /** 提示内容 */
  message: string;
  /** 提示类型 */
  type?: 'success' | 'info' | 'warning' | 'error';
  /** 是否显示 */
  open: boolean;
  /** 自动关闭时间(毫秒)，0表示不自动关闭 */
  duration?: number;
  /** 关闭回调 */
  onClose?: () => void;
  /** 位置 */
  position?: 'top' | 'top-right' | 'top-left' | 'bottom' | 'bottom-right' | 'bottom-left';
}

export const Toast: React.FC<ToastProps> = ({
  message,
  type = 'info',
  open,
  duration = 3000,
  onClose,
  position = 'top',
}) => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (open) {
      setVisible(true);
      if (duration > 0) {
        const timer = setTimeout(() => {
          handleClose();
        }, duration);
        return () => clearTimeout(timer);
      }
    } else {
      setVisible(false);
    }
    return () => {}; // 确保所有路径都返回一个函数
  }, [open, duration]); // eslint-disable-line react-hooks/exhaustive-deps -- handleClose在useEffect内部定义，不需要作为依赖

  const handleClose = useCallback(() => {
    setVisible(false);
    setTimeout(() => {
      onClose?.();
    }, 300); // 等待动画完成
  }, [onClose]);

  if (!open && !visible) {
    return null;
  }

  // 根据类型设置图标和颜色
  const getTypeConfig = () => {
    switch (type) {
      case 'success':
        return {
          icon: <CheckCircle size={20} />,
          color: '#10b981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          borderColor: 'rgba(16, 185, 129, 0.3)',
        };
      case 'warning':
        return {
          icon: <AlertCircle size={20} />,
          color: '#f59e0b',
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          borderColor: 'rgba(245, 158, 11, 0.3)',
        };
      case 'error':
        return {
          icon: <AlertCircle size={20} />,
          color: '#ef4444',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          borderColor: 'rgba(239, 68, 68, 0.3)',
        };
      default:
        return {
          icon: <CheckCircle size={20} />,
          color: '#3b82f6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderColor: 'rgba(59, 130, 246, 0.3)',
        };
    }
  };

  // 根据位置设置样式
  const getPositionStyle = (): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      position: 'fixed',
      zIndex: 10001,
      transform: visible ? 'translateY(0)' : 'translateY(-20px)',
      opacity: visible ? 1 : 0,
      transition: 'all 0.3s ease-in-out',
    };

    switch (position) {
      case 'top':
        return {
          ...baseStyle,
          top: '20px',
          left: '50%',
          transform: `translateX(-50%) ${visible ? 'translateY(0)' : 'translateY(-20px)'}`,
        };
      case 'top-right':
        return { ...baseStyle, top: '20px', right: '20px' };
      case 'top-left':
        return { ...baseStyle, top: '20px', left: '20px' };
      case 'bottom':
        return {
          ...baseStyle,
          bottom: '20px',
          left: '50%',
          transform: `translateX(-50%) ${visible ? 'translateY(0)' : 'translateY(20px)'}`,
        };
      case 'bottom-right':
        return {
          ...baseStyle,
          bottom: '20px',
          right: '20px',
          transform: visible ? 'translateY(0)' : 'translateY(20px)',
        };
      case 'bottom-left':
        return {
          ...baseStyle,
          bottom: '20px',
          left: '20px',
          transform: visible ? 'translateY(0)' : 'translateY(20px)',
        };
      default:
        return {
          ...baseStyle,
          top: '20px',
          left: '50%',
          transform: `translateX(-50%) ${visible ? 'translateY(0)' : 'translateY(-20px)'}`,
        };
    }
  };

  const typeConfig = getTypeConfig();

  return (
    <div style={getPositionStyle()}>
      <div
        style={{
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(20px) saturate(180%)',
          border: `1px solid ${typeConfig.borderColor}`,
          borderRadius: '12px',
          padding: '12px 16px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          minWidth: '200px',
          maxWidth: '400px',
        }}
      >
        <div style={{ color: typeConfig.color }}>{typeConfig.icon}</div>

        <div
          style={{
            flex: 1,
            color: '#1e293b',
            fontSize: '14px',
            fontWeight: '500',
            lineHeight: '1.4',
          }}
        >
          {message}
        </div>

        {duration === 0 && (
          <button
            onClick={handleClose}
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              color: '#64748b',
              padding: '4px',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'color 0.2s ease',
            }}
            onMouseEnter={e => {
              e.currentTarget.style.color = '#1e293b';
            }}
            onMouseLeave={e => {
              e.currentTarget.style.color = '#64748b';
            }}
          >
            <X size={16} />
          </button>
        )}
      </div>
    </div>
  );
};

/**
 * Toast管理器 Hook
 */
export const useToast = () => {
  const [toasts, setToasts] = useState<Array<ToastProps & { id: string }>>([]);

  const showToast = (props: Omit<ToastProps, 'open' | 'onClose'>) => {
    const id = Date.now().toString();
    const toast: ToastProps & { id: string } = {
      ...props,
      id,
      open: true,
      onClose: () => {
        setToasts(prev => prev.filter(t => t.id !== id));
      },
    };
    setToasts(prev => [...prev, toast]);
  };

  const ToastContainer = () => (
    <>
      {toasts.map(toast => (
        <Toast key={toast.id} {...toast} />
      ))}
    </>
  );

  return {
    showToast,
    ToastContainer,
  };
};
