# 重庆银行AI助手原型项目技术交接文档

## 1. 项目概览

### 1.1 项目基本信息
- **项目名称**: 重庆银行AI问询助手
- **项目描述**: 基于虚拟数字人的智能银行业务咨询系统，提供语音交互、业务指引、智能问答等功能
- **技术栈**: HTML5 + CSS3 + 原生JavaScript
- **开发模式**: 原型开发，面向大屏展示（1920×1080分辨率优化）

### 1.2 核心功能特性
- 🎭 **虚拟数字人交互**: 支持视频播放、语音合成、动态状态切换
- 🗣️ **多语言支持**: 普通话/川渝话双语切换
- 🎨 **多主题模式**: 暗色/明亮/关爱模式三种主题
- 🎤 **语音交互**: 支持语音识别和语音合成
- 💼 **业务智能指引**: 涵盖银行全业务场景的智能导航
- 📱 **响应式设计**: 适配多种分辨率（1080p-4K）

### 1.3 文件结构
```
tanqi/
├── bank-virtual-assistant.html    # 主页面文件
├── bank-virtual-assistant.css     # 样式文件（7945行）
├── bank-virtual-assistant.js      # 脚本文件（1730行）
├── home_副本.html                 # 银行首页（备用）
├── gif/                          # 收音动画资源
│   ├── anjian.gif               # 按键收音动画
│   └── chundon.gif              # 唇动收音动画
├── human/                        # 数字人视频资源
│   ├── speak/                   # 说话状态视频
│   │   ├── speak1.mp4
│   │   ├── speak2.mp4
│   │   ├── speak3.mp4
│   │   └── speak4.mp4
│   └── wait/                    # 待机状态视频
│       ├── 2.5Ddaiji1.mp4
│       ├── 2.5Ddaiji2.mp4
│       └── 2.5Ddaiji3.mp4
├── speak/                        # 说话视频（备用）
└── wait/                         # 待机视频（备用）
```

## 2. 核心组件分析

### 2.1 页面布局架构
```
main-container (主容器)
├── header (顶部导航栏)
│   ├── left-control-buttons (左侧控制按钮)
│   ├── h1 (标题)
│   ├── ai-badge (AI标识)
│   └── control-buttons (右侧功能按钮)
└── content-area (内容区域)
    ├── business-display (业务展示区)
    │   ├── quick-functions (常用功能区)
    │   └── business-grid (业务卡片网格)
    ├── virtual-assistant-area (虚拟人展示区)
    │   ├── avatar-container (头像容器)
    │   ├── assistant-status (状态显示)
    │   └── feature-bubbles (特色功能标签)
    └── right-panel (右侧面板)
        ├── chat-area (聊天区)
        └── recommendation-area (智能推荐区)
```

### 2.2 关键UI组件

#### 2.2.1 头部导航组件 (.header)
- **功能**: 品牌展示、主题切换、语言切换、收音模式切换、关爱模式
- **关键元素**:
  - 返回首页按钮 (#homeBtn)
  - 主题切换按钮 (#themeToggle)
  - 语言切换按钮 (#languageToggle)
  - 收音方式切换 (#audioInputToggle)
  - 关爱模式切换 (#careToggle)

#### 2.2.2 虚拟数字人组件 (.virtual-assistant-area)
- **功能**: 数字人视频播放、状态显示、语音识别反馈
- **关键元素**:
  - 视频播放器 (#avatarVideo)
  - 收音动画 (#audioGif)
  - 语音识别显示 (#speechRecognition)
  - 语音波形动画 (#voiceWaves)
  - 状态文本 (#statusText, #statusDesc)

#### 2.2.3 业务展示组件 (.business-display)
- **功能**: 常用功能快捷入口、业务分类导航
- **关键元素**:
  - 常用功能网格 (.quick-grid)
  - 业务卡片网格 (.business-grid)
  - 全部功能入口 (#allFunctionsCard)

#### 2.2.4 聊天交互组件 (.chat-area)
- **功能**: 对话消息显示、用户输入、语音输入
- **关键元素**:
  - 消息容器 (#chatMessages)
  - 输入框 (#chatInput)
  - 发送按钮 (#sendBtn)
  - 语音按钮 (#voiceBtn)

#### 2.2.5 智能推荐组件 (.recommendation-area)
- **功能**: 基于对话内容的智能推荐
- **关键元素**:
  - 推荐网格 (#recommendationGrid)

## 3. 样式系统架构

### 3.1 CSS变量设计系统
```css
:root {
    /* 主色调 */
    --primary-color: #03a9f4;
    --accent-color: #ffc837;
    --care-text-color: #2c3e50;
    
    /* 文本颜色 */
    --text-primary: #fff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    
    /* 背景色 */
    --bg-dark: #0a0e27;
    --bg-light: #f8fafc;
    
    /* 动画过渡 */
    --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* 圆角系统 */
    --border-radius-sm: 8px;
    --border-radius-md: 16px;
    --border-radius-lg: 20px;
    --border-radius-xl: 50px;
    
    /* 阴影系统 */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 8px 32px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 16px 48px rgba(0, 0, 0, 0.2);
    
    /* 玻璃效果 */
    --glass-bg-light: rgba(255, 255, 255, 0.15);
    --glass-bg-dark: rgba(255, 255, 255, 0.08);
    --glass-border: rgba(255, 255, 255, 0.25);
}
```

### 3.2 主题切换机制
- **暗色主题** (默认): `body` 基础样式
- **明亮主题**: `body.light-theme` 类
- **关爱模式**: `body.care-theme` 类（大字体、高对比度）

### 3.3 响应式断点系统
- **4K分辨率**: `@media (min-width: 2560px)`
- **2K分辨率**: `@media (max-width: 2559px) and (min-width: 1920px)`
- **1080p分辨率**: `@media (max-width: 1919px) and (min-width: 1600px)`
- **小屏适配**: `@media (max-width: 1599px)`

### 3.4 关键布局模式
- **Flexbox主布局**: 三列等宽布局（业务区 + 虚拟人区 + 聊天区）
- **Grid网格系统**: 业务卡片和功能按钮使用CSS Grid
- **玻璃拟态效果**: backdrop-filter + 半透明背景
- **流体动画**: CSS transitions + transform

## 4. JavaScript功能架构

### 4.1 全局状态管理
```javascript
// 核心状态变量
let isRecording = false;              // 录音状态
let currentBusiness = null;           // 当前业务类型
let conversationContext = [];         // 对话上下文
let avatarVideoManager = null;        // 视频管理器
let audioInputMode = 'lip';           // 收音模式
let isCareMode = false;              // 关爱模式状态
let currentLanguage = 'mandarin';     // 当前语言
```

### 4.2 核心功能模块

#### 4.2.1 虚拟人视频管理 (AvatarVideoManager)
```javascript
class AvatarVideoManager {
    constructor() {
        this.videoElement = document.getElementById('avatarVideo');
        this.currentState = 'idle';
        this.videoSources = {
            idle: ['wait/2.5Ddaiji1.mp4', 'wait/2.5Ddaiji2.mp4', 'wait/2.5Ddaiji3.mp4'],
            speaking: ['speak/speak1.mp4', 'speak/speak2.mp4', 'speak/speak3.mp4', 'speak/speak4.mp4']
        };
    }
    
    switchToIdle() { /* 切换到待机状态 */ }
    switchToSpeaking() { /* 切换到说话状态 */ }
    destroy() { /* 清理资源 */ }
}
```

#### 4.2.2 语音交互模块
- **语音合成**: `speak(text)` - 支持普通话/川渝话
- **语音识别**: 模拟实现，支持按键/唇动两种收音模式
- **收音动画**: GIF动画展示收音状态

#### 4.2.3 主题切换模块
- **主题切换**: `toggleTheme()` - 暗色/明亮模式切换
- **关爱模式**: `toggleCareMode()` - 大字体、高对比度
- **语言切换**: `toggleLanguage()` - 普通话/川渝话切换

#### 4.2.4 业务流程模块
- **业务选择**: `selectBusiness(business)` - 业务类型选择
- **快捷功能**: `selectQuickFunction(functionKey)` - 常用功能选择
- **智能推荐**: `updateRecommendations(userMessage)` - 动态推荐更新

#### 4.2.5 对话管理模块
- **消息发送**: `sendMessage()` - 用户消息处理
- **AI回复**: `generateAIResponse(message)` - 智能回复生成
- **消息显示**: `addMessage(message, sender)` - 消息渲染

### 4.3 事件处理机制
- **DOM事件绑定**: 页面加载完成后统一绑定事件
- **状态持久化**: localStorage保存用户偏好设置
- **错误处理**: try-catch包装关键操作
- **性能优化**: 防抖处理、资源清理

## 5. 核心业务流程

### 5.1 用户交互主流程

### 5.2 页面跳转逻辑
- **返回首页**: `homeBtn` → `./home.html`
- **投资理财专区**: 业务选择 `investment` → `./ai agent 金融业务/ai-wealth-advisor-demo.html`
- **错误处理**: try-catch包装，失败时使用 `window.open()` 备用方案

### 5.3 状态管理流程
- **初始化**: 从localStorage读取用户偏好（主题、语言、关爱模式）
- **状态更新**: 实时更新UI状态和虚拟人状态
- **持久化**: 用户操作后自动保存到localStorage

## 6. 技术要点详解

### 6.1 虚拟数字人实现

#### 6.1.1 视频资源管理
```javascript
// 视频资源配置
const videoSources = {
    idle: ['wait/2.5Ddaiji1.mp4', 'wait/2.5Ddaiji2.mp4', 'wait/2.5Ddaiji3.mp4'],
    speaking: ['speak/speak1.mp4', 'speak/speak2.mp4', 'speak/speak3.mp4', 'speak/speak4.mp4']
};

// 状态切换逻辑
switchToSpeaking() {
    this.currentState = 'speaking';
    const randomVideo = this.getRandomVideo('speaking');
    this.loadVideo(randomVideo);
}
```

#### 6.1.2 语音合成集成
```javascript
function speak(text) {
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'zh-CN';

    // 川渝话参数调整
    if (currentLanguage === 'chuanyu') {
        utterance.rate = 0.9;    // 语速稍慢
        utterance.pitch = 1.1;   // 音调稍高
    }

    // 同步虚拟人动画
    utterance.onstart = () => avatarVideoManager.switchToSpeaking();
    utterance.onend = () => avatarVideoManager.switchToIdle();
}
```

### 6.2 语音识别模拟实现

#### 6.2.1 收音模式切换
```javascript
// 收音模式配置
const audioInputModes = {
    lip: { gif: 'gif/chundon.gif', text: '唇动收音' },
    key: { gif: 'gif/anjian.gif', text: '按键收音' }
};

// 模式切换逻辑
function toggleAudioInputMode() {
    audioInputMode = audioInputMode === 'lip' ? 'key' : 'lip';
    updateAudioInputUI();
    localStorage.setItem('audioInputMode', audioInputMode);
}
```

#### 6.2.2 语音识别动画
```javascript
function startMockSpeechRecognition() {
    const phrases = [
        '我想查询账户余额',
        '帮我办理转账业务',
        '开通手机银行',
        '申请信用卡'
    ];

    const selectedPhrase = phrases[Math.floor(Math.random() * phrases.length)];
    simulateTyping(selectedPhrase, 150); // 150ms每字的打字效果
}
```

### 6.3 主题系统实现

#### 6.3.1 CSS变量动态切换
```css
/* 暗色主题（默认） */
body {
    --bg-primary: #0a0e27;
    --text-primary: #fff;
}

/* 明亮主题 */
body.light-theme {
    --bg-primary: #f8fafc;
    --text-primary: #1976d2;
}

/* 关爱模式 */
body.care-theme {
    --font-size-base: 18px;  /* 放大20% */
    --contrast-ratio: high;
}
```

#### 6.3.2 主题切换动画
```javascript
function toggleTheme() {
    const body = document.body;
    const isLight = body.classList.contains('light-theme');

    // 添加切换动画
    body.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
    body.classList.toggle('light-theme');

    // 更新按钮状态
    updateThemeButton(!isLight);

    // 保存偏好
    localStorage.setItem('theme', isLight ? 'dark' : 'light');
}
```

### 6.4 响应式布局实现

#### 6.4.1 Flexbox三列布局
```css
.content-area {
    display: flex;
    gap: 20px;
    height: calc(100vh - 80px);
}

.business-display { flex: 1; }
.virtual-assistant-area { flex: 1; }
.right-panel { flex: 1; }
```

#### 6.4.2 关爱模式布局调整
```css
body.care-theme .business-display {
    flex: 1.08 1 0 !important;  /* 增加宽度 */
}

body.care-theme .virtual-assistant-area {
    flex: 1.15 1 0 !important;  /* 增加宽度 */
}

body.care-theme .chat-area {
    flex: 1.15 !important;      /* 调整高度比例 */
    min-height: 170px !important;
}
```

### 6.5 性能优化策略

#### 6.5.1 资源懒加载
```javascript
// 视频预加载
function preloadVideos() {
    const videos = [...videoSources.idle, ...videoSources.speaking];
    videos.forEach(src => {
        const video = document.createElement('video');
        video.preload = 'metadata';
        video.src = src;
    });
}
```

#### 6.5.2 事件防抖处理
```javascript
// 防止快速点击
let isToggling = false;
function toggleTheme() {
    if (isToggling) return;
    isToggling = true;

    // 执行切换逻辑
    performThemeToggle();

    setTimeout(() => {
        isToggling = false;
    }, 500);
}
```

#### 6.5.3 内存管理
```javascript
// 页面卸载时清理资源
window.addEventListener('beforeunload', function() {
    if (avatarVideoManager) {
        avatarVideoManager.destroy();
    }

    // 清理定时器
    if (speechRecognitionTimeout) {
        clearTimeout(speechRecognitionTimeout);
    }
});
```

## 7. 数据结构设计

### 7.1 业务数据结构
```javascript
// 业务推荐数据结构
const businessRecommendations = {
    account: [
        { title: '开卡', desc: '申请新银行卡' },
        { title: '卡激活', desc: '激活新申请的银行卡' }
    ],
    inquiry: [
        { title: '账户查询', desc: '查看账户余额信息' },
        { title: '交易明细查询', desc: '查看交易记录明细' }
    ]
    // ... 其他业务类型
};
```

### 7.2 对话上下文管理
```javascript
// 对话上下文结构
let conversationContext = [
    {
        timestamp: Date.now(),
        user: '我想查询余额',
        assistant: '好的，我来为您查询账户余额...',
        business: 'inquiry'
    }
];
```

### 7.3 状态管理结构
```javascript
// 应用状态
const appState = {
    theme: 'dark',           // 'dark' | 'light'
    language: 'mandarin',    // 'mandarin' | 'chuanyu'
    careMode: false,         // boolean
    audioInputMode: 'lip',   // 'lip' | 'key'
    currentBusiness: null,   // string | null
    isRecording: false       // boolean
};
```

## 8. 开发规范建议

### 8.1 代码组织规范
- **模块化**: 将功能拆分为独立的类和函数
- **命名规范**: 使用驼峰命名法，语义化命名
- **注释规范**: 关键功能添加中文注释
- **错误处理**: 关键操作使用try-catch包装

### 8.2 样式编写规范
- **CSS变量**: 统一使用CSS自定义属性
- **BEM命名**: 组件样式使用BEM命名规范
- **响应式**: 移动端优先的响应式设计
- **性能优化**: 避免重复样式，合理使用选择器

### 8.3 资源管理规范
- **图片优化**: 使用WebP格式，适当压缩
- **视频优化**: H.264编码，合理分辨率
- **字体加载**: 使用font-display: swap
- **缓存策略**: 静态资源添加缓存头

## 9. 部署和维护

### 9.1 部署要求
- **Web服务器**: 支持静态文件服务（Nginx/Apache）
- **HTTPS**: 语音功能需要HTTPS环境
- **浏览器兼容**: Chrome 80+, Firefox 75+, Safari 13+
- **分辨率支持**: 1920×1080 ~ 3840×2160

### 9.2 性能监控
- **加载时间**: 首屏加载时间 < 3秒
- **交互响应**: 用户操作响应时间 < 200ms
- **内存使用**: 长时间运行内存稳定
- **错误监控**: JavaScript错误日志收集

### 9.3 维护要点
- **定期更新**: 浏览器API兼容性检查
- **内容更新**: 业务数据和推荐内容更新
- **性能优化**: 定期检查和优化加载性能
- **用户反馈**: 收集用户使用反馈，持续改进

## 10. 后续开发建议

### 10.1 功能增强
- **真实语音识别**: 集成Web Speech API
- **AI对话**: 接入真实的AI对话服务
- **数据持久化**: 用户对话历史保存
- **多语言扩展**: 支持更多方言和语言

### 10.2 技术升级
- **框架迁移**: 考虑迁移到React/Vue框架
- **TypeScript**: 添加类型检查提高代码质量
- **PWA**: 支持离线使用和桌面安装
- **WebRTC**: 实现真实的音视频通话

### 10.3 用户体验优化
- **个性化**: 用户偏好学习和推荐
- **无障碍**: 提升无障碍访问支持
- **动画优化**: 更流畅的过渡动画
- **手势支持**: 触摸设备手势操作

---

## 附录：关键文件说明

### A.1 主要文件功能
- `bank-virtual-assistant.html`: 主页面结构，包含所有UI组件
- `bank-virtual-assistant.css`: 完整样式系统，包含主题和响应式
- `bank-virtual-assistant.js`: 核心业务逻辑和交互功能
- `home_副本.html`: 银行首页，用于页面跳转

### A.2 资源文件说明
- `gif/`: 收音动画资源（anjian.gif按键收音，chundon.gif唇动收音）
- `human/speak/`: 数字人说话状态视频（4个随机播放）
- `human/wait/`: 数字人待机状态视频（3个随机播放）

### A.3 关键配置参数
- 默认分辨率: 1920×1080
- 视频格式: MP4 (H.264)
- 动画格式: GIF
- 字体: Microsoft YaHei
- 主色调: #03a9f4 (蓝色), #ffc837 (黄色)

---

*本文档为重庆银行AI助手原型项目的完整技术交接文档，涵盖了项目的核心架构、关键技术实现和开发维护要点。建议前端开发人员在接手项目前仔细阅读，如有疑问可随时沟通。*
