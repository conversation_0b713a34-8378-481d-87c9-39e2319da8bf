# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 构建产物 (只忽略JS构建文件，保留静态资源)
dist/web-service-sdk.js
dist/web-service-sdk.js.map
dist/*.css
dist/*.css.map
dist/index.js
dist/index.d.ts
dist/index.esm.js

# 运行时文件
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage/
*.lcov
.nyc_output

# 测试输出
test-results/
playwright-report/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 缓存目录
.cache/
.parcel-cache/
.vite/

# IDE 配置文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
logs/
*.log

# 临时文件
tmp/
temp/
.tmp/

# 备份文件
*.bak
*.backup
*.old

# Python 相关 (mock server)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Python 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# TypeScript 编译缓存
*.tsbuildinfo

# ESLint 缓存
.eslintcache

# Prettier 缓存
.prettiercache

# Stylelint 缓存
.stylelintcache

# 包管理器锁文件 (保留 pnpm-lock.yaml)
package-lock.json
yarn.lock

# 构建工具缓存
.rollup.cache/
.turbo/

# 本地配置文件
.local
local.config.js
local.config.ts

# 测试相关
.vitest/
coverage.json

# 文档生成
docs/api/
docs/coverage/

# 发布相关
*.tgz
*.tar.gz

# 调试文件
debug.log
error.log

# 性能分析
*.cpuprofile
*.heapprofile

# 安全相关
.env.*.local
secrets.json
private.key
*.pem

# 自定义忽略
.custom/
local/
private/
