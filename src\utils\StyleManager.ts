/**
 * 样式管理器 - 使用Constructable Stylesheets的最佳实践
 * 解决Lit组件样式问题的终极方案
 */

/**
 * 样式管理器类
 */
export class StyleManager {
  private static instance: StyleManager;
  private tailwindSheet: CSSStyleSheet | null = null;
  private componentSheet: CSSStyleSheet | null = null;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): StyleManager {
    if (!StyleManager.instance) {
      StyleManager.instance = new StyleManager();
    }
    return StyleManager.instance;
  }

  /**
   * 初始化样式表
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // 检查浏览器支持
      if (!('adoptedStyleSheets' in Document.prototype)) {
        // eslint-disable-next-line no-console -- StyleManager 需要输出调试信息
        console.warn('浏览器不支持adoptedStyleSheets，使用回退方案');
        return this.initializeFallback();
      }

      // 创建Tailwind样式表
      this.tailwindSheet = new CSSStyleSheet();
      await this.tailwindSheet.replace(this.getTailwindCSS());

      // 创建组件样式表
      this.componentSheet = new CSSStyleSheet();
      await this.componentSheet.replace(this.getComponentCSS());

      this.isInitialized = true;
      // eslint-disable-next-line no-console -- StyleManager 需要输出调试信息
      console.log('✅ 样式管理器初始化完成');
    } catch (error) {
      // eslint-disable-next-line no-console -- StyleManager 需要输出调试信息
      console.error('❌ 样式管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 为Shadow Root应用样式
   */
  public applyToShadowRoot(shadowRoot: ShadowRoot): void {
    if (!this.isInitialized || !this.tailwindSheet || !this.componentSheet) {
      // eslint-disable-next-line no-console -- StyleManager 需要输出调试信息
      console.warn('样式表未初始化');
      return;
    }

    try {
      shadowRoot.adoptedStyleSheets = [this.tailwindSheet, this.componentSheet];
      // eslint-disable-next-line no-console -- StyleManager 需要输出调试信息
      console.log('✅ 样式已应用到Shadow Root');
    } catch (error) {
      // eslint-disable-next-line no-console -- StyleManager 需要输出调试信息
      console.error('❌ 应用样式失败:', error);
    }
  }

  /**
   * 回退方案：注入style标签
   */
  private initializeFallback(): void {
    // 为不支持adoptedStyleSheets的浏览器提供回退
    this.isInitialized = true;
    // eslint-disable-next-line no-console -- StyleManager 需要输出调试信息
    console.log('✅ 样式管理器初始化完成（回退方案）');
  }

  /**
   * 为不支持adoptedStyleSheets的浏览器注入样式
   */
  public injectStylesForFallback(shadowRoot: ShadowRoot): void {
    const style = document.createElement('style');
    style.textContent = this.getTailwindCSS() + '\n' + this.getComponentCSS();
    shadowRoot.appendChild(style);
  }

  /**
   * 获取Tailwind CSS
   */
  private getTailwindCSS(): string {
    return `
      /* Tailwind CSS 基础样式 - 精简版 */
      *, ::before, ::after {
        box-sizing: border-box;
        border-width: 0;
        border-style: solid;
        border-color: #e5e7eb;
      }

      /* 布局类 */
      .flex { display: flex; }
      .flex-col { flex-direction: column; }
      .flex-row-reverse { flex-direction: row-reverse; }
      .flex-1 { flex: 1 1 0%; }
      .flex-shrink-0 { flex-shrink: 0; }
      .items-center { align-items: center; }
      .items-start { align-items: flex-start; }
      .justify-center { justify-content: center; }
      .self-start { align-self: flex-start; }
      .self-end { align-self: flex-end; }

      /* 尺寸类 */
      .w-full { width: 100%; }
      .w-8 { width: 2rem; }
      .w-2 { width: 0.5rem; }
      .h-full { height: 100%; }
      .h-8 { height: 2rem; }
      .h-2 { height: 0.5rem; }
      .w-\\[420px\\] { width: 420px; }
      .h-\\[880px\\] { height: 880px; }
      .w-\\[120px\\] { width: 120px; }
      .h-\\[160px\\] { height: 160px; }
      .max-w-\\[85\\%\\] { max-width: 85%; }

      /* 间距类 */
      .p-3 { padding: 0.75rem; }
      .p-4 { padding: 1rem; }
      .p-6 { padding: 1.5rem; }
      .px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
      .px-4 { padding-left: 1rem; padding-right: 1rem; }
      .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
      .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
      .mb-2 { margin-bottom: 0.5rem; }
      .mt-1 { margin-top: 0.25rem; }
      .mr-2 { margin-right: 0.5rem; }

      /* 间距组合类 */
      .space-x-2 > :not([hidden]) ~ :not([hidden]) { margin-left: 0.5rem; }
      .space-x-3 > :not([hidden]) ~ :not([hidden]) { margin-left: 0.75rem; }
      .space-x-reverse > :not([hidden]) ~ :not([hidden]) { margin-right: 0.5rem; margin-left: 0; }
      .space-y-3 > :not([hidden]) ~ :not([hidden]) { margin-top: 0.75rem; }

      /* 背景色类 */
      .bg-white { background-color: rgb(255 255 255); }
      .bg-gray-50 { background-color: rgb(249 250 251); }
      .bg-gray-100 { background-color: rgb(243 244 246); }
      .bg-gray-400 { background-color: rgb(156 163 175); }
      .bg-gray-700 { background-color: rgb(55 65 81); }
      .bg-gray-800 { background-color: rgb(31 41 55); }
      .bg-gray-900 { background-color: rgb(17 24 39); }
      .bg-blue-500 { background-color: rgb(59 130 246); }
      .bg-blue-600 { background-color: rgb(37 99 235); }
      .bg-green-500 { background-color: rgb(34 197 94); }
      .bg-red-500 { background-color: rgb(239 68 68); }
      .bg-yellow-100 { background-color: rgb(254 249 195); }

      /* 渐变背景 */
      .bg-gradient-to-b { background-image: linear-gradient(to bottom, var(--tw-gradient-stops)); }
      .from-gray-800 { --tw-gradient-from: rgb(31 41 55); --tw-gradient-to: rgb(31 41 55 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
      .to-gray-900 { --tw-gradient-to: rgb(17 24 39); }
      .from-blue-50 { --tw-gradient-from: rgb(239 246 255); --tw-gradient-to: rgb(239 246 255 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
      .to-white { --tw-gradient-to: rgb(255 255 255); }

      /* 文字颜色类 */
      .text-white { color: rgb(255 255 255); }
      .text-gray-200 { color: rgb(229 231 235); }
      .text-gray-300 { color: rgb(209 213 219); }
      .text-gray-400 { color: rgb(156 163 175); }
      .text-gray-500 { color: rgb(107 114 128); }
      .text-gray-600 { color: rgb(75 85 99); }
      .text-gray-800 { color: rgb(31 41 55); }
      .text-gray-900 { color: rgb(17 24 39); }
      .text-blue-100 { color: rgb(219 234 254); }
      .text-blue-600 { color: rgb(37 99 235); }
      .text-yellow-800 { color: rgb(133 77 14); }

      /* 边框类 */
      .border { border-width: 1px; }
      .border-b { border-bottom-width: 1px; }
      .border-t { border-top-width: 1px; }
      .border-gray-200 { border-color: rgb(229 231 235); }
      .border-gray-600 { border-color: rgb(75 85 99); }
      .border-gray-700 { border-color: rgb(55 65 81); }
      .rounded-lg { border-radius: 0.5rem; }
      .rounded-full { border-radius: 9999px; }

      /* 字体类 */
      .font-sans { font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif; }
      .font-semibold { font-weight: 600; }
      .font-bold { font-weight: 700; }
      .font-medium { font-weight: 500; }
      .text-xs { font-size: 0.75rem; line-height: 1rem; }
      .text-sm { font-size: 0.875rem; line-height: 1.25rem; }
      .text-center { text-align: center; }
      .leading-relaxed { line-height: 1.625; }

      /* 其他类 */
      .overflow-hidden { overflow: hidden; }
      .overflow-y-auto { overflow-y: auto; }
      .object-cover { object-fit: cover; }
      .inline-block { display: inline-block; }
      .cursor-not-allowed { cursor: not-allowed; }
      .transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
      .duration-200 { transition-duration: 200ms; }

      /* 伪类 */
      .hover\\:bg-blue-600:hover { background-color: rgb(37 99 235); }
      .focus\\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
      .focus\\:ring-2:focus { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }
      .focus\\:ring-blue-500:focus { --tw-ring-opacity: 1; --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity)); }
      .focus\\:ring-offset-2:focus { --tw-ring-offset-width: 2px; }
      .focus\\:border-blue-500:focus { border-color: rgb(59 130 246); }

      /* 输入框样式 */
      input::placeholder { color: rgb(156 163 175); }
      .placeholder-gray-400::placeholder { color: rgb(156 163 175); }
      .placeholder-gray-500::placeholder { color: rgb(107 114 128); }

      /* 禁用状态 */
      button:disabled { opacity: 0.5; cursor: not-allowed; }
    `;
  }

  /**
   * 获取组件特定样式
   */
  private getComponentCSS(): string {
    return `
      /* 组件特定样式 */
      :host {
        display: block;
        width: 420px;
        height: 880px;
        font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
      }

      /* 确保所有元素使用正确的字体 */
      * {
        font-family: inherit;
      }

      /* 输入框和按钮的基础样式 */
      input, textarea, button {
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
      }

      /* 滚动条样式 */
      .overflow-y-auto::-webkit-scrollbar {
        width: 6px;
      }

      .overflow-y-auto::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      .overflow-y-auto::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }

      .overflow-y-auto::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }
    `;
  }
}
