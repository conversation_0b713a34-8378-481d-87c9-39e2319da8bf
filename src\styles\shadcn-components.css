/**
 * shadcn/ui 组件样式 - 适配Web Components
 * 直接提取shadcn/ui的组件样式定义
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
}
}

@layer components {

  /* Button 组件 */
  .btn {
    @apply inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-default {
    @apply bg-primary text-primary-foreground shadow hover:bg-primary/90;
    @apply h-9 px-4 py-2;
  }

  .btn-destructive {
    @apply bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90;
    @apply h-9 px-4 py-2;
  }

  .btn-outline {
    @apply border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground;
    @apply h-9 px-4 py-2;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80;
    @apply h-9 px-4 py-2;
  }

  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground;
    @apply h-9 px-4 py-2;
  }

  .btn-link {
    @apply text-primary underline-offset-4 hover:underline;
    @apply h-9 px-4 py-2;
  }

  .btn-sm {
    @apply h-8 rounded-md px-3 text-xs;
  }

  .btn-lg {
    @apply h-10 rounded-md px-8;
  }

  .btn-icon {
    @apply h-9 w-9;
  }

  /* Input 组件 */
  .input {
    @apply flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50;
  }

  /* Card 组件 */
  .card {
    @apply rounded-xl border bg-card text-card-foreground shadow;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-title {
    @apply font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-muted-foreground;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  /* Avatar 组件 */
  .avatar {
    @apply relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full;
  }

  .avatar-image {
    @apply aspect-square h-full w-full;
  }

  .avatar-fallback {
    @apply flex h-full w-full items-center justify-center rounded-full bg-muted;
  }

  /* Badge 组件 */
  .badge {
    @apply inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .badge-default {
    @apply border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80;
  }

  .badge-secondary {
    @apply border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }

  .badge-destructive {
    @apply border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80;
  }

  .badge-outline {
    @apply text-foreground;
  }

  /* Separator 组件 */
  .separator {
    @apply shrink-0 bg-border;
  }

  .separator-horizontal {
    @apply h-[1px] w-full;
  }

  .separator-vertical {
    @apply h-full w-[1px];
  }

  /* 聊天相关的自定义组件 */
  .chat-container {
    @apply flex flex-col h-full bg-background text-foreground;
  }

  .chat-header {
    @apply flex items-center justify-between p-4 border-b bg-card;
  }

  .chat-messages {
    @apply flex-1 overflow-y-auto p-4 space-y-4;
  }

  .chat-message {
    @apply flex items-start space-x-3 max-w-[85%];
  }

  .chat-message-user {
    @apply self-end flex-row-reverse space-x-reverse;
  }

  .chat-message-assistant {
    @apply self-start;
  }

  .chat-avatar {
    @apply flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-primary text-primary-foreground text-xs font-bold;
  }

  .chat-bubble {
    @apply rounded-lg p-3 text-sm leading-relaxed shadow-sm;
  }

  .chat-bubble-user {
    @apply bg-primary text-primary-foreground;
  }

  .chat-bubble-assistant {
    @apply bg-muted text-muted-foreground;
  }

  .chat-timestamp {
    @apply text-xs opacity-70 mt-1;
  }

  .chat-input-area {
    @apply border-t bg-card p-4;
  }

  .chat-input-container {
    @apply flex items-center space-x-3;
  }

  /* 数字人头像区域 */
  .avatar-section {
    @apply flex-shrink-0 p-6 bg-gradient-to-b from-muted/50 to-background;
  }

  .avatar-container {
    @apply flex justify-center;
  }

  .avatar-info {
    @apply text-center space-y-2;
  }

  .avatar-image-large {
    @apply w-[120px] h-[160px] object-cover rounded-lg shadow-md;
  }

  .avatar-name {
    @apply text-sm font-semibold text-primary;
  }

  .avatar-version {
    @apply text-xs text-muted-foreground;
  }

  /* 状态指示器 */
  .status-indicator {
    @apply inline-flex items-center space-x-2;
  }

  .status-dot {
    @apply w-2 h-2 rounded-full;
  }

  .status-online {
    @apply bg-green-500;
  }

  .status-offline {
    @apply bg-red-500;
  }

  .status-text {
    @apply text-sm font-medium;
  }

  /* 加载状态 */
  .loading-container {
    @apply flex justify-center py-4;
  }

  .loading-text {
    @apply text-sm text-muted-foreground;
  }

  /* 系统消息 */
  .system-message {
    @apply flex justify-center py-2;
  }

  .system-badge {
    @apply bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-xs;
  }

  /* 响应式调整 */
  @media (max-width: 640px) {
    .chat-container {
      @apply h-screen;
    }

    .avatar-section {
      @apply p-4;
    }

    .avatar-image-large {
      @apply w-[100px] h-[130px];
    }
  }
}