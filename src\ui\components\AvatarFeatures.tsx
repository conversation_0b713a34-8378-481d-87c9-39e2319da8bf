/**
 * 虚拟人描述卡片组件
 * 包含数字人特性展示
 */

import { Mic, Search, Shield, Clock } from 'lucide-react';
import React from 'react';

interface FeatureCard {
  icon: React.ComponentType<{ size?: number; style?: React.CSSProperties }>;
  title: string;
  color: string;
}

interface AvatarFeaturesProps {
  className?: string;
  style?: React.CSSProperties;
  layoutMode?: 'standard' | 'care'; // 新增：布局模式
}

export const AvatarFeatures: React.FC<AvatarFeaturesProps> = ({
  className,
  style,
  layoutMode = 'standard',
}) => {
  const features: FeatureCard[] = [
    {
      icon: Mic,
      title: '能听能讲重庆话，交流更巴适',
      color: '#007AFF',
    },
    {
      icon: Search,
      title: '业务智能搜引：快速找到需要的银行服务',
      color: '#34C759',
    },
    {
      icon: Shield,
      title: '金融业务知识问答',
      color: '#FF9500',
    },
    {
      icon: Clock,
      title: '7x24小时在线',
      color: '#AF52DE',
    },
  ];

  return (
    <div
      className={className}
      style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(2, 1fr)',
        gap: layoutMode === 'care' ? '16px' : '12px', // 关爱模式增加间距
        maxWidth: '100%',
        margin: '0 auto',
        ...style,
      }}
    >
      {features.map((feature, index) => {
        const IconComponent = feature.icon;
        return (
          <div
            key={index}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: layoutMode === 'care' ? '12px' : '8px', // 关爱模式增加间距
              background: 'rgba(255, 255, 255, 0.8)',
              border:
                layoutMode === 'care'
                  ? '2px solid rgba(30, 41, 59, 0.2)'
                  : '1px solid rgba(30, 41, 59, 0.1)', // 关爱模式增强边框
              padding: layoutMode === 'care' ? '16px 20px' : '10px 12px', // 关爱模式增加内边距
              borderRadius: layoutMode === 'care' ? '16px' : '12px', // 关爱模式增大圆角
              fontSize: layoutMode === 'care' ? '16px' : '13px', // 关爱模式字体放大
              fontWeight: layoutMode === 'care' ? '600' : '500', // 关爱模式增强字重
              backdropFilter: 'blur(10px)',
              transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
              cursor: 'pointer',
            }}
            onMouseEnter={e => {
              e.currentTarget.style.transform = 'translateY(-1px)';
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.95)';
              e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
            }}
            onMouseLeave={e => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.8)';
              e.currentTarget.style.boxShadow = 'none';
            }}
          >
            <div
              style={{
                width: layoutMode === 'care' ? '40px' : '32px', // 关爱模式增大图标容器
                height: layoutMode === 'care' ? '40px' : '32px',
                borderRadius: layoutMode === 'care' ? '12px' : '8px', // 关爱模式增大圆角
                background: `${feature.color}20`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexShrink: 0,
              }}
            >
              <IconComponent
                size={layoutMode === 'care' ? 20 : 16} // 关爱模式增大图标
                style={{ color: feature.color }}
              />
            </div>
            <div
              style={{
                color: layoutMode === 'care' ? '#000000' : '#1e293b', // 关爱模式使用纯黑色文字
                fontSize: layoutMode === 'care' ? '16px' : '12px', // 关爱模式字体放大
                fontWeight: layoutMode === 'care' ? '700' : '600', // 关爱模式增强字重
                lineHeight: layoutMode === 'care' ? '1.4' : '1.3', // 关爱模式增加行高
                flex: 1,
                // 关爱模式下为文字添加阴影，增强对比度
                textShadow: layoutMode === 'care' ? '0 1px 2px rgba(0,0,0,0.1)' : 'none',
                letterSpacing: layoutMode === 'care' ? '0.3px' : 'normal', // 关爱模式增加字间距
              }}
            >
              {feature.title}
            </div>
          </div>
        );
      })}
    </div>
  );
};
