# CosyVoice2 TTS API 使用文档

## 📋 目录
- [API基本信息](#api基本信息)
- [快速开始指南](#快速开始指南)
- [流式播放实现](#流式播放实现)
- [完整前端示例](#完整前端示例)
- [错误处理](#错误处理)
- [语音指令示例](#语音指令示例)

## 🌐 API基本信息

### 服务地址
```
http://*************:8080/
```

### API端点
```
POST /inference_instruct2_sse
```

### 固定参数
- **随机种子 (seed)**: `42` (固定值，用于确保生成结果的一致性)

### 请求格式
- **Content-Type**: `multipart/form-data`
- **Accept**: `text/event-stream`

## 🚀 快速开始指南

### 必需的请求参数

| 参数名 | 类型 | 必需 | 描述 | 示例 |
|--------|------|------|------|------|
| `tts_text` | string | ✅ | 要合成的文本内容 | "收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。" |
| `instruct_text` | string | ✅ | 语音风格指令 | "用四川话说这句话" |
| `seed` | number | ✅ | 随机种子 | 42 |

### cURL 请求示例

```bash
curl -X POST "http://*************:8080/inference_instruct2_sse" \
  -H "Accept: text/event-stream" \
  -F "tts_text=收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。" \
  -F "instruct_text=用四川话说这句话" \
  -F "seed=42"
```

### JavaScript Fetch 示例

```javascript
const formData = new FormData();
formData.append('tts_text', '收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。');
formData.append('instruct_text', '用四川话说这句话');
formData.append('seed', '42');

const response = await fetch('http://*************:8080/inference_instruct2_sse', {
    method: 'POST',
    body: formData,
    headers: {
        'Accept': 'text/event-stream'
    }
});
```

## 🎵 流式播放实现

### SSE 事件类型

API 返回 Server-Sent Events (SSE) 格式的流式数据，包含以下事件类型：

1. **start** - 开始合成音频
2. **audio_chunk** - 音频数据块
3. **end** - 合成完成

### 音频块数据格式

```javascript
{
    "chunk_id": 1,           // 音频块序号
    "audio_data": "base64...", // Base64编码的WAV音频数据
    "duration": 2.5          // 音频块时长（秒）
}
```

### 流式处理核心代码

```javascript
async function processAudioStream(response) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    const audioChunks = [];
    
    while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        buffer += decoder.decode(value, { stream: true });
        
        // 处理完整的SSE事件
        while (buffer.includes('\n\n')) {
            const eventEnd = buffer.indexOf('\n\n');
            const eventData = buffer.slice(0, eventEnd);
            buffer = buffer.slice(eventEnd + 2);
            
            if (eventData.trim()) {
                const event = parseSSEEvent(eventData);
                
                if (event.type === 'start') {
                    console.log('开始合成音频...');
                } else if (event.type === 'audio_chunk') {
                    const chunkData = JSON.parse(event.data);
                    audioChunks.push(chunkData);
                    console.log(`接收音频块 ${chunkData.chunk_id}: ${chunkData.duration.toFixed(3)}秒`);
                } else if (event.type === 'end') {
                    const endData = JSON.parse(event.data);
                    console.log(`合成完成！总计 ${endData.total_chunks} 个音频块`);
                    break;
                }
            }
        }
    }
    
    return audioChunks;
}

// 解析SSE事件
function parseSSEEvent(eventText) {
    const lines = eventText.split('\n');
    let type = 'message';
    let data = '';
    
    for (const line of lines) {
        if (line.startsWith('event: ')) {
            type = line.substring(7);
        } else if (line.startsWith('data: ')) {
            data = line.substring(6);
        }
    }
    
    return { type, data };
}
```

### 音频块合并处理

```javascript
async function combineAudioChunks(chunks) {
    try {
        // 使用Web Audio API处理音频数据
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const audioBuffers = [];
        
        // 解码所有音频块
        for (let i = 0; i < chunks.length; i++) {
            const chunk = chunks[i];
            const binaryString = atob(chunk.audio_data);
            const bytes = new Uint8Array(binaryString.length);
            for (let j = 0; j < binaryString.length; j++) {
                bytes[j] = binaryString.charCodeAt(j);
            }
            
            const audioBuffer = await audioContext.decodeAudioData(bytes.buffer.slice());
            audioBuffers.push(audioBuffer);
        }
        
        // 计算总时长和采样率
        const sampleRate = audioBuffers[0].sampleRate;
        const totalDuration = audioBuffers.reduce((sum, buffer) => sum + buffer.duration, 0);
        const totalSamples = Math.ceil(totalDuration * sampleRate);
        
        // 创建合并后的音频缓冲区
        const numberOfChannels = audioBuffers[0].numberOfChannels;
        const combinedBuffer = audioContext.createBuffer(numberOfChannels, totalSamples, sampleRate);
        
        // 拼接音频数据
        let currentOffset = 0;
        for (let i = 0; i < audioBuffers.length; i++) {
            const buffer = audioBuffers[i];
            for (let channel = 0; channel < numberOfChannels; channel++) {
                const channelData = buffer.getChannelData(channel);
                const combinedChannelData = combinedBuffer.getChannelData(channel);
                
                for (let sample = 0; sample < channelData.length; sample++) {
                    if (currentOffset + sample < totalSamples) {
                        combinedChannelData[currentOffset + sample] = channelData[sample];
                    }
                }
            }
            currentOffset += buffer.length;
        }
        
        // 将AudioBuffer转换为WAV格式的Blob
        return audioBufferToWav(combinedBuffer);
        
    } catch (error) {
        console.error('Web Audio API处理失败，使用简单拼接方法:', error);
        return combineAudioChunksSimple(chunks);
    }
}
```

## 📱 完整前端示例

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS API 示例</title>
</head>
<body>
    <div>
        <h1>TTS API 测试</h1>
        <form id="ttsForm">
            <div>
                <label for="tts_text">合成文本:</label>
                <textarea id="tts_text" required>收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。</textarea>
            </div>
            <div>
                <label for="instruct_text">语音指令:</label>
                <textarea id="instruct_text" required>用四川话说这句话</textarea>
            </div>
            <button type="submit">生成语音</button>
        </form>
        
        <div id="status"></div>
        <div id="audioResult" style="display: none;">
            <h3>生成结果</h3>
            <div id="audioControls"></div>
        </div>
    </div>

    <script>
        document.getElementById('ttsForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            formData.append('tts_text', document.getElementById('tts_text').value);
            formData.append('instruct_text', document.getElementById('instruct_text').value);
            formData.append('seed', '42');
            
            const statusDiv = document.getElementById('status');
            const audioResult = document.getElementById('audioResult');
            
            try {
                statusDiv.textContent = '正在连接服务器...';
                
                const response = await fetch('http://*************:8080/inference_instruct2_sse', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Accept': 'text/event-stream'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`服务器响应错误: ${response.status}`);
                }
                
                statusDiv.textContent = '正在接收音频流...';
                
                // 处理音频流
                const audioChunks = await processAudioStream(response);
                
                if (audioChunks.length > 0) {
                    const combinedAudio = await combineAudioChunks(audioChunks);
                    displayAudioResult(combinedAudio);
                    statusDiv.textContent = '音频生成完成！';
                } else {
                    throw new Error('未接收到音频数据');
                }
                
            } catch (error) {
                console.error('生成失败:', error);
                statusDiv.textContent = `生成失败: ${error.message}`;
            }
        });
        
        function displayAudioResult(audioBlob) {
            const audioUrl = URL.createObjectURL(audioBlob);
            const audioControls = document.getElementById('audioControls');
            
            audioControls.innerHTML = `
                <audio controls>
                    <source src="${audioUrl}" type="audio/wav">
                    您的浏览器不支持音频播放。
                </audio>
                <br>
                <a href="${audioUrl}" download="tts_result.wav">下载音频文件</a>
            `;
            
            document.getElementById('audioResult').style.display = 'block';
        }
        
        // 这里需要包含之前定义的 processAudioStream, parseSSEEvent,
        // combineAudioChunks 等函数
    </script>
</body>
</html>
```

## ⚠️ 错误处理

### 常见错误类型

1. **网络连接错误**
```javascript
try {
    const response = await fetch(url, options);
    if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
    }
} catch (error) {
    if (error.name === 'TypeError') {
        console.error('网络连接失败，请检查服务器地址和网络连接');
    } else {
        console.error('请求失败:', error.message);
    }
}
```

2. **音频解码错误**
```javascript
try {
    const audioBuffer = await audioContext.decodeAudioData(bytes.buffer);
} catch (decodeError) {
    console.warn(`音频块解码失败: ${decodeError.message}`);
    // 跳过该音频块或使用备用处理方法
}
```

3. **SSE流处理错误**
```javascript
// 检查流是否正常结束
if (audioChunks.length === 0) {
    throw new Error('未接收到任何音频数据，请检查服务器状态');
}
```

### 错误处理最佳实践

```javascript
async function generateTTSWithErrorHandling(ttsText, instructText) {
    const maxRetries = 3;
    let retryCount = 0;

    while (retryCount < maxRetries) {
        try {
            const formData = new FormData();
            formData.append('tts_text', ttsText);
            formData.append('instruct_text', instructText);
            formData.append('seed', '42');

            const response = await fetch('http://*************:8080/inference_instruct2_sse', {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'text/event-stream'
                }
            });

            if (!response.ok) {
                throw new Error(`服务器错误: ${response.status}`);
            }

            const audioChunks = await processAudioStream(response);

            if (audioChunks.length === 0) {
                throw new Error('未接收到音频数据');
            }

            return await combineAudioChunks(audioChunks);

        } catch (error) {
            retryCount++;
            console.warn(`尝试 ${retryCount}/${maxRetries} 失败:`, error.message);

            if (retryCount >= maxRetries) {
                throw new Error(`生成失败，已重试 ${maxRetries} 次: ${error.message}`);
            }

            // 等待后重试
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
        }
    }
}
```

## 🎭 语音指令示例

### 方言类指令
```javascript
const dialectInstructions = [
    "用四川话说这句话",
    "用东北话说这句话",
    "用粤语说这句话",
    "用上海话说这句话",
    "用河南话说这句话"
];
```

### 语音风格指令
```javascript
const styleInstructions = [
    "请用温柔甜美的女声说话，语速稍慢",
    "请用深沉磁性的男声说话",
    "请用专业的新闻播报语调说话",
    "请用活泼可爱的儿童声音说话",
    "请用严肃正式的商务语调说话",
    "请用轻松幽默的语调说话"
];
```

### 情感表达指令
```javascript
const emotionInstructions = [
    "用开心兴奋的语调说话",
    "用悲伤低沉的语调说话",
    "用愤怒激动的语调说话",
    "用平静温和的语调说话",
    "用紧张急促的语调说话",
    "用惊讶的语调说话"
];
```

### 语速和音调指令
```javascript
const speedToneInstructions = [
    "请说得慢一些，语调温柔",
    "请说得快一些，语调活泼",
    "请用低沉的声音慢慢说",
    "请用高亢的声音快速说",
    "请用正常语速，语调平稳"
];
```

## 📊 性能优化建议

### 1. 音频缓存
```javascript
const audioCache = new Map();

function getCachedAudio(text, instruction) {
    const key = `${text}_${instruction}_42`; // 包含seed
    return audioCache.get(key);
}

function setCachedAudio(text, instruction, audioBlob) {
    const key = `${text}_${instruction}_42`;
    audioCache.set(key, audioBlob);

    // 限制缓存大小
    if (audioCache.size > 50) {
        const firstKey = audioCache.keys().next().value;
        audioCache.delete(firstKey);
    }
}
```

### 2. 流式播放优化
```javascript
// 边接收边播放，减少等待时间
async function streamingPlayback(response) {
    const audioContext = new AudioContext();
    let currentTime = 0;

    const reader = response.body.getReader();
    // ... SSE处理逻辑

    // 每接收到一个音频块就立即播放
    if (event.type === 'audio_chunk') {
        const chunkData = JSON.parse(event.data);
        const audioBuffer = await decodeAudioChunk(chunkData);

        const source = audioContext.createBufferSource();
        source.buffer = audioBuffer;
        source.connect(audioContext.destination);
        source.start(currentTime);

        currentTime += audioBuffer.duration;
    }
}
```

### 3. 内存管理
```javascript
// 及时释放音频URL
function cleanupAudioUrl(url) {
    if (url && url.startsWith('blob:')) {
        URL.revokeObjectURL(url);
    }
}

// 在组件卸载或页面离开时清理
window.addEventListener('beforeunload', () => {
    audioCache.clear();
    // 清理所有blob URL
});
```

## 🔧 调试工具

### 启用详细日志
```javascript
const DEBUG = true;

function debugLog(message, data = null) {
    if (DEBUG) {
        console.log(`[TTS Debug] ${message}`, data);
    }
}

// 在关键位置添加调试信息
debugLog('开始发送请求', { ttsText, instructText });
debugLog('接收到音频块', chunkData);
debugLog('音频合并完成', { totalChunks: audioChunks.length, totalSize: audioBlob.size });
```

### 性能监控
```javascript
function performanceMonitor() {
    const startTime = performance.now();

    return {
        mark: (label) => {
            const currentTime = performance.now();
            console.log(`[性能] ${label}: ${(currentTime - startTime).toFixed(2)}ms`);
        },

        end: () => {
            const endTime = performance.now();
            console.log(`[性能] 总耗时: ${(endTime - startTime).toFixed(2)}ms`);
        }
    };
}

// 使用示例
const monitor = performanceMonitor();
// ... 执行TTS请求
monitor.mark('请求发送');
// ... 接收第一个音频块
monitor.mark('首个音频块');
// ... 完成音频合并
monitor.mark('音频合并完成');
monitor.end();
```

---

## 📞 技术支持

如果在使用过程中遇到问题，请检查：

1. **服务器地址是否正确**: `http://*************:8080/`
2. **网络连接是否正常**: 确保能够访问服务器
3. **浏览器兼容性**: 建议使用现代浏览器（Chrome、Firefox、Safari、Edge）
4. **CORS设置**: 确保服务器允许跨域请求
5. **音频格式支持**: 确保浏览器支持WAV格式音频播放

更多技术细节请参考源代码中的实现。
