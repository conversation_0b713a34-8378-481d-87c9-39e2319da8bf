/**
 * 聊天功能核心测试
 * 测试用户消息发送、AI响应接收、流式消息处理和会话管理
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { testUtils } from '../setup';

describe('聊天功能测试', () => {
  let mockEventBus: any;
  let mockSDK: any;
  let sessionId: string;

  beforeEach(() => {
    // 创建模拟的EventBus
    mockEventBus = testUtils.createMockEventBus();
    sessionId = testUtils.generateTestId('session');

    // 创建模拟的SDK实例
    mockSDK = {
      getEventBus: () => mockEventBus,
      getStatus: () => ({ isReady: true }),
      generateRequestId: () => testUtils.generateTestId('req'),
      sendJsonRpcMessage: vi.fn().mockResolvedValue({ result: 'success' }),
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('用户消息发送', () => {
    it('应该能够发送用户消息', () => {
      const userInput = '你好，我想了解银行服务';
      const requestId = mockSDK.generateRequestId();

      // 模拟发送用户消息
      mockEventBus.emit('ai:send-chat-request', {
        userInput,
        sessionId,
        requestId,
      });

      // 验证事件被正确发送
      expect(mockEventBus.emit).toHaveBeenCalledWith('ai:send-chat-request', {
        userInput,
        sessionId,
        requestId,
      });
    });

    it('应该能够处理用户输入事件', () => {
      const userInput = '我想查询账户余额';
      const requestId = testUtils.generateTestId('req');
      let receivedInput: any = null;

      // 注册监听器
      mockEventBus.on('greeting-page:user-input', (data: any) => {
        receivedInput = data;
      });

      // 发送用户输入事件
      mockEventBus.emit('greeting-page:user-input', {
        userInput,
        sessionId,
        requestId,
      });

      // 验证事件被正确接收
      expect(receivedInput).toEqual({
        userInput,
        sessionId,
        requestId,
      });
    });
  });

  describe('AI响应接收', () => {
    it('应该能够接收AI响应', () => {
      const aiMessage = '您好！我可以帮您查询账户余额。请提供您的账户信息。';
      const requestId = testUtils.generateTestId('req');
      let receivedResponse: any = null;

      // 注册监听器
      mockEventBus.on('greeting-page:ai-response', (data: any) => {
        receivedResponse = data;
      });

      // 模拟AI响应
      mockEventBus.emit('greeting-page:ai-response', {
        message: aiMessage,
        sessionId,
        requestId,
      });

      // 验证响应被正确接收
      expect(receivedResponse).toEqual({
        message: aiMessage,
        sessionId,
        requestId,
      });
    });

    it('应该能够处理AI响应错误', () => {
      const errorMessage = 'AI服务暂时不可用';
      const requestId = testUtils.generateTestId('req');
      let receivedError: any = null;

      // 注册错误监听器
      mockEventBus.on('ai:error', (data: any) => {
        receivedError = data;
      });

      // 模拟AI错误响应
      mockEventBus.emit('ai:error', {
        error: errorMessage,
        sessionId,
        requestId,
      });

      // 验证错误被正确处理
      expect(receivedError).toEqual({
        error: errorMessage,
        sessionId,
        requestId,
      });
    });
  });

  describe('流式消息处理', () => {
    it('应该能够处理流式消息块', () => {
      const messageId = testUtils.generateTestId('msg');
      const chunks = ['您好', '！我是', '数字人', '助手。'];
      const receivedChunks: any[] = [];

      // 注册流式消息监听器
      mockEventBus.on('ai:stream-message-chunk', (data: any) => {
        receivedChunks.push(data);
      });

      // 发送流式消息块
      chunks.forEach((chunk, index) => {
        mockEventBus.emit('ai:stream-message-chunk', {
          messageId,
          sessionId,
          chunk,
          isComplete: index === chunks.length - 1,
        });
      });

      // 验证所有块都被接收
      expect(receivedChunks).toHaveLength(chunks.length);
      expect(receivedChunks.map(c => c.chunk)).toEqual(chunks);
      expect(receivedChunks[receivedChunks.length - 1].isComplete).toBe(true);
    });

    it('应该能够处理流式消息完成', () => {
      const messageId = testUtils.generateTestId('msg');
      const finalMessage = '完整的AI响应消息';
      let completedMessage: any = null;

      // 注册流式消息完成监听器
      mockEventBus.on('ai:stream-message-complete', (data: any) => {
        completedMessage = data;
      });

      // 发送流式消息完成事件
      mockEventBus.emit('ai:stream-message-complete', {
        messageId,
        sessionId,
        content: finalMessage,
      });

      // 验证完成事件被正确处理
      expect(completedMessage).toEqual({
        messageId,
        sessionId,
        content: finalMessage,
      });
    });
  });

  describe('会话管理', () => {
    it('应该能够创建新会话', () => {
      const newSessionId = testUtils.generateTestId('session');
      let sessionCreated: any = null;

      // 注册会话创建监听器
      mockEventBus.on('session:created', (data: any) => {
        sessionCreated = data;
      });

      // 创建新会话
      mockEventBus.emit('session:created', {
        sessionId: newSessionId,
        timestamp: Date.now(),
      });

      // 验证会话创建事件
      expect(sessionCreated.sessionId).toBe(newSessionId);
      expect(sessionCreated.timestamp).toBeTypeOf('number');
    });

    it('应该能够切换会话', () => {
      const oldSessionId = sessionId;
      const newSessionId = testUtils.generateTestId('session');
      let sessionSwitched: any = null;

      // 注册会话切换监听器
      mockEventBus.on('session:switched', (data: any) => {
        sessionSwitched = data;
      });

      // 切换会话
      mockEventBus.emit('session:switched', {
        oldSessionId,
        newSessionId,
        timestamp: Date.now(),
      });

      // 验证会话切换事件
      expect(sessionSwitched.oldSessionId).toBe(oldSessionId);
      expect(sessionSwitched.newSessionId).toBe(newSessionId);
    });

    it('应该能够清理会话', () => {
      let sessionCleared: any = null;

      // 注册会话清理监听器
      mockEventBus.on('session:cleared', (data: any) => {
        sessionCleared = data;
      });

      // 清理会话
      mockEventBus.emit('session:cleared', {
        sessionId,
        timestamp: Date.now(),
      });

      // 验证会话清理事件
      expect(sessionCleared.sessionId).toBe(sessionId);
    });
  });

  describe('消息状态管理', () => {
    it('应该能够跟踪消息状态', () => {
      const messageStates = ['pending', 'delivered', 'read', 'error'];
      const messageId = testUtils.generateTestId('msg');
      const receivedStates: string[] = [];

      // 注册消息状态监听器
      mockEventBus.on('message:status-changed', (data: any) => {
        receivedStates.push(data.status);
      });

      // 模拟消息状态变化
      messageStates.forEach(status => {
        mockEventBus.emit('message:status-changed', {
          messageId,
          sessionId,
          status,
          timestamp: Date.now(),
        });
      });

      // 验证状态变化被正确跟踪
      expect(receivedStates).toEqual(messageStates);
    });
  });
});
