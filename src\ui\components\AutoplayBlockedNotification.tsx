/**
 * TTS自动播放被阻止时的简单提示组件
 */

import { Volume2 } from 'lucide-react';
import React from 'react';

import { Button } from '../../components/ui/button';

interface AutoplayBlockedNotificationProps {
  /** 是否显示提示 */
  open: boolean;
  /** 用户点击启用音频的回调 */
  onEnableAudio?: () => void;
}

/**
 * 简单的自动播放被阻止提示
 */
export const AutoplayBlockedNotification: React.FC<AutoplayBlockedNotificationProps> = ({
  open,
  onEnableAudio,
}) => {
  if (!open) {
    return null;
  }

  const handleEnableAudio = () => {
    onEnableAudio?.();
  };

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000,
        backdropFilter: 'blur(8px)',
      }}
      onClick={handleEnableAudio}
    >
      <div
        style={{
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(20px) saturate(180%)',
          padding: '24px',
          borderRadius: '12px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          textAlign: 'center',
          maxWidth: '400px',
          width: '90%',
          border: '1px solid rgba(30, 41, 59, 0.1)',
        }}
        onClick={e => e.stopPropagation()}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '8px',
            marginBottom: '16px',
          }}
        >
          <Volume2 size={24} style={{ color: '#007AFF' }} />
          <h3
            style={{
              margin: 0,
              color: '#1e293b',
              fontSize: '18px',
              fontWeight: '600',
            }}
          >
            需要启用音频播放
          </h3>
        </div>

        <p
          style={{
            margin: '0 0 20px 0',
            color: '#64748b',
            fontSize: '14px',
            lineHeight: '1.5',
          }}
        >
          浏览器阻止了自动播放音频，请点击下方按钮启用语音播放功能。
        </p>

        <Button
          onClick={handleEnableAudio}
          size="lg"
          style={{
            width: '100%',
            background: 'rgba(0, 122, 255, 0.1)',
            color: '#007AFF',
            border: '1px solid #007AFF40',
            fontSize: '16px',
            fontWeight: '600',
          }}
          onMouseEnter={e => {
            e.currentTarget.style.transform = 'translateY(-1px)';
          }}
          onMouseLeave={e => {
            e.currentTarget.style.transform = 'translateY(0)';
          }}
        >
          启用音频播放
        </Button>
      </div>
    </div>
  );
};
