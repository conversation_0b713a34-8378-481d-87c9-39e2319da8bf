/**
 * React版本的ChatWidget组件
 * 基于GreetingPageReact的成功模式，保持与Lit版本功能完全一致
 */

import { Mic, MicOff, Volume2 } from 'lucide-react';
import React, { useState, useEffect, useRef, useCallback } from 'react';

import { WebSDK } from '../core/WebSDK';
import { generateUUID } from '../utils/helpers';
import { Logger, LogLevel } from '../utils/Logger';

// 导入现有的React组件
import { AutoplayBlockedNotification } from './components/AutoplayBlockedNotification';
import { SimpleChatInterface } from './components/SimpleChatInterface';
import {
  SimpleDigitalHumanReact,
  SimpleDigitalHumanReactRef,
} from './components/SimpleDigitalHumanReact';
import { useToast } from './components/Toast';

// 导入全局样式
import '../styles/globals.css';

interface ChatWidgetReactProps {
  sdk: WebSDK;
  theme?: 'light' | 'dark';
  maxMessages?: number;
  enableVoiceInput?: boolean;
  avatarUrl?: string;
  defaultVoice?: string;
}

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  sessionId: string;
  status: 'pending' | 'sending' | 'sent' | 'delivered' | 'failed' | 'streaming';
  createdAt: number;
  updatedAt: number;
}

export const ChatWidgetReact: React.FC<ChatWidgetReactProps> = ({
  sdk,
  theme = 'light',
  maxMessages = 100,
  enableVoiceInput: _enableVoiceInput = true,
  avatarUrl: _avatarUrl = '',
  defaultVoice: _defaultVoice = 'mandarin',
}) => {
  // 状态管理（与Lit版本保持一致）
  const [messages, setMessages] = useState<Message[]>([]);
  const [connectionStatus, setConnectionStatus] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showAutoplayNotification, setShowAutoplayNotification] = useState(false);

  // 新增：收音模式和语言状态管理
  const [microphoneMode, setMicrophoneMode] = useState<'button' | 'lip'>('button');
  const [language, setLanguage] = useState<'mandarin' | 'chuanyu'>('mandarin');

  // Toast管理
  const { showToast, ToastContainer } = useToast();

  // 引用和常量
  const sessionIdRef = useRef<string>(generateUUID());
  const loggerRef = useRef(
    Logger.getInstance({
      prefix: 'ChatWidgetReact',
      level: sdk?.getConfig().debug ? LogLevel.DEBUG : LogLevel.INFO,
    })
  );
  const isStartedRef = useRef(false);
  const digitalHumanRef = useRef<SimpleDigitalHumanReactRef>(null);

  // 流式消息状态管理
  const [, setStreamingMessages] = useState<Map<string, { content: string; messageId: string }>>(
    new Map()
  );

  // 避免未使用变量警告，这些属性保留用于未来扩展
  // 移除重复的配置日志以减少噪音
  // 开发环境配置信息已移除以符合lint规范

  // 初始化收音模式和语言状态
  useEffect(() => {
    const savedMode = localStorage.getItem('chat-widget-microphone-mode') as 'button' | 'lip';
    if (savedMode && (savedMode === 'button' || savedMode === 'lip')) {
      setMicrophoneMode(savedMode);
    } else {
      // 默认设置为按键收音模式
      setMicrophoneMode('button');
      localStorage.setItem('chat-widget-microphone-mode', 'button');
    }

    const savedLanguage = localStorage.getItem('websdk-language') as 'mandarin' | 'chuanyu';
    const finalLanguage =
      savedLanguage && (savedLanguage === 'mandarin' || savedLanguage === 'chuanyu')
        ? savedLanguage
        : 'mandarin';

    setLanguage(finalLanguage);

    if (!savedLanguage) {
      localStorage.setItem('websdk-language', 'mandarin');
    }

    // 只有在GreetingPage不存在时才通知ServiceCoordinator（避免冲突）
    setTimeout(() => {
      const greetingPageExists =
        document.querySelector('[data-component="greeting-page"]') !== null;
      if (!greetingPageExists && sdk && sdk.getStatus().isReady) {
        sdk.getEventBus().emit('tts:language-change', { language: finalLanguage });
        loggerRef.current.info('🗣️ ChatWidget初始化时通知语言状态', {
          language: finalLanguage,
          reason: 'GreetingPage不存在',
        });
      }
    }, 100); // 延迟检查，确保DOM已渲染
  }, [sdk]);

  // 监听其他组件的语言变化
  useEffect(() => {
    if (!sdk || !sdk.getStatus().isReady) {
      return;
    }

    const eventBus = sdk.getEventBus();
    const logger = loggerRef.current;

    const handleLanguageChangeFromOtherComponent = (data: unknown) => {
      const eventData = data as { language: string; source: string };
      const { language, source } = eventData;
      // 只响应来自其他组件的语言变化，避免循环
      // 如果是来自GreetingPage的变化，优先接受（组件优先级）
      if (
        source !== 'chat-widget' &&
        language &&
        (language === 'mandarin' || language === 'chuanyu')
      ) {
        logger.info('🔄 ChatWidget收到其他组件的语言变化通知', { language, source });
        setLanguage(language);
        // 同步更新localStorage
        localStorage.setItem('websdk-language', language);
      }
    };

    eventBus.on('ui:language-change', handleLanguageChangeFromOtherComponent);

    return () => {
      eventBus.off('ui:language-change', handleLanguageChangeFromOtherComponent);
    };
  }, [sdk]);

  // 监听收音模式变化，确保数字人组件同步更新
  useEffect(() => {
    if (digitalHumanRef.current && microphoneMode) {
      digitalHumanRef.current.switchMicrophoneMode(microphoneMode);
    }
  }, [microphoneMode]);

  // 处理收音模式切换
  const handleMicrophoneModeChange = async (mode: 'button' | 'lip') => {
    const logger = loggerRef.current;
    logger.info('🎤 ChatWidget收音模式切换', { mode });

    setMicrophoneMode(mode);
    digitalHumanRef.current?.switchMicrophoneMode(mode);

    // 存储到localStorage以保持状态
    localStorage.setItem('chat-widget-microphone-mode', mode);

    // 发送JSON-RPC请求通知HKSTT服务收音模式变化
    try {
      const speakMode = mode === 'lip' ? 0 : 1; // 0=唇动收音，1=点击收音
      const requestId = generateUUID();

      logger.info('📤 发送收音模式通知到HKSTT服务', { mode, speakMode, requestId });

      const response = await sdk.sendJsonRpcMessage({
        jsonrpc: '2.0',
        method: 'speakMode',
        params: {
          speakMode: speakMode,
        },
        id: requestId,
      });

      logger.info('✅ 收音模式通知发送成功', { mode, speakMode, response });
    } catch (error) {
      logger.error('❌ 发送收音模式通知失败', { mode, error });
      // 不影响UI切换，只记录错误
    }

    // 显示切换提示
    showToast({
      message: `已切换到${mode === 'button' ? '按键收音' : '唇动收音'}模式`,
      type: 'success',
      position: 'top',
      duration: 2000,
    });
  };

  // 处理语言模式切换
  const handleLanguageChange = (newLanguage: 'mandarin' | 'chuanyu') => {
    const logger = loggerRef.current;

    // 检查是否有GreetingPage存在（通过DOM检查）
    const greetingPageExists = document.querySelector('[data-component="greeting-page"]') !== null;

    if (greetingPageExists) {
      logger.warn('⚠️ ChatWidget检测到GreetingPage存在，语言切换被阻止', {
        requestedLanguage: newLanguage,
        reason: 'GreetingPage优先级更高',
      });

      showToast({
        message: '请在主页面切换语言模式',
        type: 'warning',
        position: 'top',
        duration: 3000,
      });
      return;
    }

    logger.info('🗣️ ChatWidget语言模式切换', { language: newLanguage });

    setLanguage(newLanguage);

    // 存储到localStorage以保持状态
    localStorage.setItem('websdk-language', newLanguage);

    // 通过EventBus通知TTS服务语言模式变化
    if (sdk && sdk.getStatus().isReady) {
      sdk.getEventBus().emit('tts:language-change', { language: newLanguage });
      // 通知其他组件语言状态变化
      sdk
        .getEventBus()
        .emit('ui:language-change', { language: newLanguage, source: 'chat-widget' });
    }

    // 显示切换提示
    showToast({
      message: `已切换到${newLanguage === 'mandarin' ? '普通话' : '川渝话'}模式`,
      type: 'success',
      position: 'top',
      duration: 2000,
    });
  };

  // 添加消息到列表
  const addMessage = useCallback(
    (message: Message) => {
      setMessages(prev => {
        const newMessages = [...prev, message];
        // 限制消息数量
        if (newMessages.length > maxMessages) {
          return newMessages.slice(-maxMessages);
        }
        return newMessages;
      });
    },
    [maxMessages]
  );

  // 更新流式消息显示（参考GreetingPageReact实现）
  const updateStreamingMessage = useCallback(
    (messageChunk: string, requestId: string, isPartial: boolean) => {
      loggerRef.current.debug('🔄 更新流式消息显示', {
        requestId,
        messageChunk: messageChunk.substring(0, 50) + '...',
        messageLength: messageChunk.length,
        isPartial,
      });

      setStreamingMessages(prevStreamingMessages => {
        const newStreamingMessages = new Map(prevStreamingMessages);
        let streamingState = newStreamingMessages.get(requestId);

        if (!streamingState) {
          // 创建新的流式消息
          const messageId = `streaming_${requestId}`;
          streamingState = {
            content: messageChunk,
            messageId,
          };
          newStreamingMessages.set(requestId, streamingState);

          // 添加到消息列表（作为AI消息）
          const streamingMessage: Message = {
            id: messageId,
            role: 'assistant',
            content: messageChunk,
            sessionId: sessionIdRef.current,
            status: 'streaming', // 标记为流式状态
            createdAt: Date.now(),
            updatedAt: Date.now(),
          };

          setMessages(prev => [...prev, streamingMessage]);
          loggerRef.current.info('📝 创建新的流式消息', { messageId, content: messageChunk });
        } else {
          // 更新现有流式消息 - 修复：直接替换内容而不是累积
          // 因为服务器发送的可能是完整内容而不是增量内容
          streamingState.content = messageChunk;

          // 更新消息列表中的对应消息
          const currentStreamingState = streamingState; // 保存引用避免TypeScript警告
          setMessages(prev =>
            prev.map(msg => {
              if (msg.id === currentStreamingState.messageId) {
                return {
                  ...msg,
                  content: currentStreamingState.content,
                  status: 'streaming', // 始终保持streaming状态，直到收到最终响应
                  updatedAt: Date.now(),
                };
              }
              return msg;
            })
          );

          loggerRef.current.debug('🔄 更新流式消息内容', {
            messageId: currentStreamingState.messageId,
            contentLength: currentStreamingState.content.length,
            isPartial,
          });
        }

        // 如果不是部分消息，清理流式状态并更新消息状态为完成
        if (!isPartial) {
          const finalContent = streamingState.content;
          const finalMessageId = streamingState.messageId;

          newStreamingMessages.delete(requestId);
          loggerRef.current.info('✅ 流式消息完成，更新状态为delivered', {
            requestId,
            finalMessageId,
            contentLength: finalContent.length,
          });

          // 立即更新消息状态为完成
          setMessages(prev =>
            prev.map(msg => {
              if (msg.id === finalMessageId) {
                return {
                  ...msg,
                  content: finalContent,
                  status: 'delivered' as const,
                  updatedAt: Date.now(),
                };
              }
              return msg;
            })
          );
        }

        return newStreamingMessages;
      });
    },
    [setMessages, setStreamingMessages]
  );

  // 处理AI响应
  const handleAIResponse = useCallback((data: unknown) => {
    const response = data as { message: string; sessionId: string; requestId: string };

    loggerRef.current.info('📥 ChatWidgetReact收到AI响应事件', {
      message: response.message,
      eventSessionId: response.sessionId,
      mySessionId: sessionIdRef.current,
      requestId: response.requestId,
      sessionMatch: response.sessionId === sessionIdRef.current,
      messageLength: response.message?.length || 0,
    });

    // 严格会话匹配：只处理属于当前会话的响应
    if (response.sessionId !== sessionIdRef.current) {
      loggerRef.current.warn('⚠️ AI响应会话ID不匹配，忽略响应', {
        expectedSessionId: sessionIdRef.current,
        receivedSessionId: response.sessionId,
        requestId: response.requestId,
      });
      return;
    }

    if (response.message && response.message.trim()) {
      // 先清理流式消息状态，避免状态不一致
      setStreamingMessages(prev => {
        const newMap = new Map(prev);
        newMap.delete(response.requestId);
        return newMap;
      });

      // 处理最终响应消息
      setMessages(prev => {
        // 查找是否存在对应的流式消息
        const existingStreamingMessage = prev.find(
          msg => msg.id === `streaming_${response.requestId}` && msg.status === 'streaming'
        );

        if (existingStreamingMessage) {
          // 如果已经有流式消息，更新其状态为完成
          loggerRef.current.info('✅ 更新流式消息状态为完成', {
            requestId: response.requestId,
            messageId: existingStreamingMessage.id,
            finalContentLength: response.message.length,
          });

          return prev.map(msg => {
            if (msg.id === `streaming_${response.requestId}`) {
              return {
                ...msg,
                content: response.message,
                status: 'delivered' as const,
                updatedAt: Date.now(),
              };
            }
            return msg;
          });
        } else {
          // 如果没有流式消息，检查是否已经存在相同requestId的消息（防止重复）
          const existingMessage = prev.find(
            msg =>
              msg.id === `ai-${response.requestId}` ||
              (msg.role === 'assistant' && msg.content === response.message)
          );

          if (existingMessage) {
            loggerRef.current.warn('⚠️ 检测到重复的AI响应，跳过添加', {
              requestId: response.requestId,
              existingMessageId: existingMessage.id,
            });
            return prev;
          }

          // 添加新的完整消息
          loggerRef.current.info('📝 添加新的AI响应消息', {
            requestId: response.requestId,
            contentLength: response.message.length,
          });

          return [
            ...prev,
            {
              id: `ai-${response.requestId}`, // 使用requestId确保唯一性
              role: 'assistant',
              content: response.message,
              sessionId: sessionIdRef.current,
              status: 'delivered',
              createdAt: Date.now(),
              updatedAt: Date.now(),
            },
          ];
        }
      });

      setIsLoading(false);
    } else {
      loggerRef.current.warn('❌ AI响应内容为空，跳过处理', {
        message: response.message,
      });
    }
  }, []);

  // 处理用户输入（ASR）
  const handleUserInput = useCallback(
    (data: unknown) => {
      const input = data as { userInput: string; sessionId: string; requestId: string };

      loggerRef.current.info('📥 收到用户输入事件', {
        userInput: input.userInput,
        eventSessionId: input.sessionId,
        mySessionId: sessionIdRef.current,
        requestId: input.requestId,
        sessionMatch: input.sessionId === sessionIdRef.current,
      });

      // 严格会话匹配：只处理属于当前会话的用户输入
      if (input.sessionId !== sessionIdRef.current) {
        loggerRef.current.warn('⚠️ 用户输入会话ID不匹配，忽略输入', {
          expectedSessionId: sessionIdRef.current,
          receivedSessionId: input.sessionId,
          requestId: input.requestId,
        });
        return;
      }

      addMessage({
        id: `user-${Date.now()}`,
        role: 'user',
        content: input.userInput,
        sessionId: sessionIdRef.current,
        status: 'delivered',
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
      loggerRef.current.info('✅ ASR用户输入已添加到消息列表', { content: input.userInput });
    },
    [addMessage]
  );

  // 处理流式消息（参考GreetingPageReact实现）
  const handleStreamMessageChunk = useCallback(
    (data: unknown) => {
      const chunk = data as {
        message: string;
        sessionId: string;
        requestId: string;
        isPartial: boolean;
      };

      loggerRef.current.debug('📈 ChatWidget收到流式消息片段', {
        message: chunk.message,
        eventSessionId: chunk.sessionId,
        mySessionId: sessionIdRef.current,
        requestId: chunk.requestId,
        sessionMatch: chunk.sessionId === sessionIdRef.current,
        messageLength: chunk.message?.length || 0,
        isPartial: chunk.isPartial,
      });

      // 如果sessionId匹配，实时更新消息显示
      if (chunk.sessionId === sessionIdRef.current && chunk.message && chunk.message.trim()) {
        updateStreamingMessage(chunk.message, chunk.requestId, chunk.isPartial);
      }
    },
    [updateStreamingMessage]
  );

  // TTS自动播放事件处理函数
  const handleAutoplayBlocked = useCallback((data: unknown) => {
    loggerRef.current.info('🚫 ChatWidgetReact收到TTS自动播放被阻止事件', { data });
    setShowAutoplayNotification(true);
  }, []);

  const handleAutoplayResumed = useCallback((data: unknown) => {
    loggerRef.current.info('✅ ChatWidgetReact收到TTS自动播放恢复事件', { data });
    setShowAutoplayNotification(false);
  }, []);

  // 初始化和事件绑定
  useEffect(() => {
    if (!sdk || !sdk.getStatus().isReady) {
      loggerRef.current.warn('SDK未就绪，等待初始化');
      return;
    }

    // 防止重复启动
    if (isStartedRef.current) {
      loggerRef.current.warn('ChatWidgetReact已启动，跳过重复启动');
      return;
    }

    loggerRef.current.info('启动ChatWidgetReact', { sessionId: sessionIdRef.current });

    const eventBus = sdk.getEventBus();

    // 获取SDK状态
    const sdkStatus = sdk.getStatus();
    setConnectionStatus(sdkStatus.isConnected);

    // 【关键】切换到自定义组件场景，让路由器设置sessionId
    loggerRef.current.info('🔄 切换到自定义组件场景', { sessionId: sessionIdRef.current });
    eventBus.emit('router:switch-scenario', {
      scenario: 'custom-component',
      sessionId: sessionIdRef.current,
    });

    // 定义事件处理函数
    const connectionStatusHandler = (data: unknown) => {
      const status = data as { connected: boolean };
      setConnectionStatus(status.connected);
    };

    // 绑定事件监听器（与Lit版本完全一致的事件名）
    eventBus.on('connection:status', connectionStatusHandler);
    eventBus.on('custom-component:ai-response', handleAIResponse);
    eventBus.on('custom-component:user-input', handleUserInput);
    eventBus.on('ai:stream-message-chunk', handleStreamMessageChunk);

    // 绑定TTS自动播放事件监听器
    loggerRef.current.info('🎧 ChatWidgetReact绑定TTS自动播放事件监听器');
    eventBus.on('tts:autoplay-blocked', handleAutoplayBlocked);
    eventBus.on('tts:autoplay-resumed', handleAutoplayResumed);

    // 监听会话ID同步事件
    const handleSessionSync = (data: unknown) => {
      const syncData = data as { sessionId: string; reason: string };
      loggerRef.current.info('🔄 收到会话ID同步事件', {
        oldSessionId: sessionIdRef.current,
        newSessionId: syncData.sessionId,
        reason: syncData.reason,
      });

      // 更新sessionId
      sessionIdRef.current = syncData.sessionId;

      loggerRef.current.info('✅ 会话ID已同步', {
        sessionId: sessionIdRef.current,
        reason: syncData.reason,
      });
    };
    eventBus.on('custom-component:session-sync', handleSessionSync);

    isStartedRef.current = true;
    loggerRef.current.info('ChatWidgetReact已成功启动');

    // 清理函数
    return () => {
      eventBus.off('connection:status', connectionStatusHandler);
      eventBus.off('custom-component:ai-response', handleAIResponse);
      eventBus.off('custom-component:user-input', handleUserInput);
      eventBus.off('ai:stream-message-chunk', handleStreamMessageChunk);
      eventBus.off('tts:autoplay-blocked', handleAutoplayBlocked);
      eventBus.off('tts:autoplay-resumed', handleAutoplayResumed);
      eventBus.off('custom-component:session-sync', handleSessionSync);
    };
  }, [
    sdk,
    handleAIResponse,
    handleUserInput,
    handleStreamMessageChunk,
    updateStreamingMessage,
    handleAutoplayBlocked,
    handleAutoplayResumed,
  ]);

  // 处理启用音频播放
  const handleEnableAudio = useCallback(() => {
    const logger = loggerRef.current;
    logger.info('🎵 用户点击启用音频播放');

    if (!sdk || !sdk.getStatus().isReady) {
      logger.warn('SDK未就绪，无法启用音频');
      return;
    }

    // 立即在同步执行阶段通知用户已交互（关键：必须在真实用户事件的同步阶段）
    const eventBus = sdk.getEventBus();
    eventBus.emit('user:interaction-detected', {
      source: 'autoplay-notification-click',
      timestamp: Date.now(),
    });
    logger.info('🎵 用户交互事件已立即发送');

    // 方法1：创建一个真实的音频并播放来激活音频上下文
    const audio = new Audio();
    // 使用一个很短的静音音频来激活音频上下文
    audio.src =
      'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmHgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';

    const playPromise = audio.play();

    if (playPromise !== undefined) {
      playPromise
        .then(() => {
          logger.info('✅ 音频上下文已激活');
          audio.pause();
          audio.currentTime = 0;
          logger.info('🎵 音频播放权限已获取，TTS应该可以自动播放了');
        })
        .catch(error => {
          logger.warn('音频播放失败，但用户交互已记录', { error });
        });
    }

    // 隐藏提示
    setShowAutoplayNotification(false);

    logger.info('🎵 处理完成，等待TTS自动播放');
  }, [sdk]);

  // 发送消息处理
  const handleSendMessage = useCallback(
    (text: string) => {
      if (!text.trim() || isLoading || !sdk || !sdk.getStatus().isReady) {
        return;
      }

      const requestId = generateUUID();
      const eventBus = sdk.getEventBus();

      // 发送用户输入通知给SDK内部监听器
      eventBus.emit('jsonrpc:notification', {
        jsonrpc: '2.0',
        method: 'notifications/userInput',
        params: {
          userInput: text,
          requestId: requestId,
          sessionId: sessionIdRef.current,
        },
      });

      // 添加用户消息到界面
      addMessage({
        id: `user-${Date.now()}`,
        role: 'user',
        content: text,
        sessionId: sessionIdRef.current,
        status: 'delivered',
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      // 设置加载状态
      setIsLoading(true);

      // 发送消息到AI服务
      eventBus.emit('ai:send-chat-request', {
        userInput: text,
        sessionId: sessionIdRef.current,
        requestId: requestId,
      });

      loggerRef.current.info('发送用户输入消息', { text, requestId });
    },
    [isLoading, sdk, addMessage]
  );

  // 渲染侧边栏控制按钮
  const renderSidebarControls = () => (
    <div
      style={{
        position: 'absolute',
        left: '8px',
        top: '50%',
        transform: 'translateY(-50%)',
        zIndex: 20,
        display: 'flex',
        flexDirection: 'column',
        gap: '8px',
      }}
    >
      {/* 收音模式切换 */}
      <button
        onClick={() => handleMicrophoneModeChange(microphoneMode === 'button' ? 'lip' : 'button')}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '6px',
          minWidth: '80px',
          height: '36px',
          borderRadius: '8px',
          fontSize: '12px',
          fontWeight: '600',
          color: microphoneMode === 'button' ? '#007AFF' : '#FF3B30',
          background:
            microphoneMode === 'button' ? 'rgba(0, 122, 255, 0.1)' : 'rgba(255, 59, 48, 0.1)',
          border: `1px solid ${microphoneMode === 'button' ? '#007AFF' : '#FF3B30'}40`,
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          backdropFilter: 'blur(10px)',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          padding: '0 8px',
        }}
        title={microphoneMode === 'button' ? '切换到唇动收音' : '切换到按键收音'}
        onMouseEnter={e => {
          e.currentTarget.style.transform = 'scale(1.05)';
        }}
        onMouseLeave={e => {
          e.currentTarget.style.transform = 'scale(1)';
        }}
      >
        {microphoneMode === 'button' ? <Mic size={16} /> : <MicOff size={16} />}
        <span style={{ whiteSpace: 'nowrap' }}>
          {microphoneMode === 'button' ? '按键收音' : '唇动收音'}
        </span>
      </button>

      {/* 语言切换 */}
      <button
        onClick={() => handleLanguageChange(language === 'mandarin' ? 'chuanyu' : 'mandarin')}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '6px',
          minWidth: '80px',
          height: '36px',
          borderRadius: '8px',
          fontSize: '12px',
          fontWeight: '600',
          color: language === 'chuanyu' ? '#FF9500' : '#34C759',
          background: language === 'chuanyu' ? 'rgba(255, 149, 0, 0.1)' : 'rgba(52, 199, 89, 0.1)',
          border: `1px solid ${language === 'chuanyu' ? '#FF9500' : '#34C759'}40`,
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          backdropFilter: 'blur(10px)',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          padding: '0 8px',
        }}
        title={language === 'chuanyu' ? '切换到普通话' : '切换到川渝话'}
        onMouseEnter={e => {
          e.currentTarget.style.transform = 'scale(1.05)';
        }}
        onMouseLeave={e => {
          e.currentTarget.style.transform = 'scale(1)';
        }}
      >
        <Volume2 size={16} />
        <span style={{ whiteSpace: 'nowrap' }}>{language === 'chuanyu' ? '川渝话' : '普通话'}</span>
      </button>
    </div>
  );

  // 渲染组件（保持与Lit版本相同的布局和样式）
  const isDark = theme === 'dark';

  return (
    <div
      data-component="chat-widget"
      style={{
        width: '420px',
        height: '880px',
        backgroundColor: isDark ? '#1f2937' : '#ffffff',
        border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
        borderRadius: '8px',
        overflow: 'hidden',
        fontFamily: 'system-ui, -apple-system, sans-serif',
        display: 'flex',
        flexDirection: 'column',
        color: isDark ? '#ffffff' : '#111827',
      }}
    >
      {/* 数字人区域 - 30%高度 */}
      <div
        style={{
          height: '30%', // 约264px (880px * 0.3)
          backgroundColor: isDark ? '#111827' : '#f8fafc',
          borderBottom: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
          flexShrink: 0, // 防止被压缩
          padding: '8px', // 添加内边距，确保视频不贴边
        }}
      >
        {/* 侧边栏控制按钮 */}
        {renderSidebarControls()}

        <SimpleDigitalHumanReact
          ref={digitalHumanRef}
          width="100%"
          height="100%"
          autoPlay={true}
          loop={true}
          microphoneMode={microphoneMode}
          usageContext="chat-widget"
          sdk={sdk}
          style={{
            maxWidth: '100%',
            maxHeight: '100%',
            borderRadius: '6px',
          }}
        />
      </div>

      {/* 聊天区域 - 70%高度 */}
      <div
        style={{
          height: '70%', // 约616px (880px * 0.7)
          display: 'flex',
          flexDirection: 'column',
          minHeight: 0,
          overflow: 'hidden', // 防止内容溢出
        }}
      >
        {/* 聊天头部 */}
        <div
          style={{
            padding: '12px 16px',
            backgroundColor: isDark ? '#1f2937' : '#f9fafb',
            borderBottom: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <div
            style={{
              width: '8px',
              height: '8px',
              backgroundColor: connectionStatus ? '#10b981' : '#ef4444',
              borderRadius: '50%',
              marginRight: '8px',
            }}
          />
          <div
            style={{
              fontSize: '14px',
              fontWeight: '600',
              color: isDark ? '#e5e7eb' : '#1f2937',
            }}
          >
            智能客服助手
          </div>
        </div>

        {/* 使用简化的聊天界面组件 */}
        <div
          style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            minHeight: 0, // 确保flex子元素可以收缩
            overflow: 'hidden', // 防止内容溢出
          }}
        >
          <SimpleChatInterface
            sdk={sdk}
            sessionId={sessionIdRef.current}
            messages={messages}
            isLoading={isLoading}
            onSendMessage={handleSendMessage}
            theme={theme}
          />
        </div>
      </div>

      {/* TTS自动播放被阻止提示 */}
      <AutoplayBlockedNotification
        open={showAutoplayNotification}
        onEnableAudio={handleEnableAudio}
      />

      {/* Toast容器 */}
      <ToastContainer />
    </div>
  );
};
