/**
 * HTTP传输层实现
 * 使用ofetch库提供现代化的HTTP客户端功能
 */

import { ofetch, FetchOptions } from 'ofetch';

import { EventBus } from '../core/EventBus';
import { Logger, LogLevel } from '../utils/Logger';

/**
 * HTTP传输配置
 */
export interface HttpTransportConfig {
  /** 基础URL */
  baseURL: string;
  /** 请求超时时间（毫秒） */
  timeout?: number;
  /** 默认请求头 */
  headers?: Record<string, string>;
  /** 重试配置 */
  retry?: {
    /** 最大重试次数 */
    maxAttempts: number;
    /** 重试间隔（毫秒） */
    delay: number;
  };
  /** 调试模式 */
  debug?: boolean;
}

/**
 * HTTP请求选项
 */
export interface HttpRequestOptions extends Omit<FetchOptions, 'baseURL'> {
  /** 是否启用重试 */
  enableRetry?: boolean;
}

/**
 * HTTP响应接口
 */
export interface HttpResponse<T = unknown> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

/**
 * HTTP传输事件
 */
export interface HttpTransportEvents {
  request: { url: string; method: string; options: HttpRequestOptions };
  response: { url: string; status: number; duration: number };
  error: { url: string; error: Error; duration: number };
}

/**
 * HTTP传输层实现
 */
export class HttpTransport {
  private config: Required<HttpTransportConfig>;
  private eventBus: EventBus;
  private logger: Logger;
  private client: typeof ofetch;

  constructor(config: HttpTransportConfig, eventBus: EventBus) {
    this.config = {
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      retry: {
        maxAttempts: 3,
        delay: 1000,
      },
      debug: false,
      ...config,
    };

    this.eventBus = eventBus;
    this.logger = Logger.getInstance({
      level: this.config.debug ? LogLevel.DEBUG : LogLevel.INFO,
      prefix: 'HttpTransport',
    });

    // 创建ofetch客户端实例
    this.client = ofetch.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: this.config.headers,
      onRequest: ({ request, options }) => {
        this.logger.debug('发送HTTP请求', {
          url: request.toString(),
          method: options.method || 'GET',
        });
        this.emitEvent('request', {
          url: request.toString(),
          method: options.method || 'GET',
          options: options as HttpRequestOptions,
        });
      },
      onResponse: ({ response }) => {
        this.logger.debug('收到HTTP响应', {
          url: response.url,
          status: response.status,
        });
      },
      onResponseError: ({ response }) => {
        this.logger.warn('HTTP响应错误', {
          url: response.url,
          status: response.status,
          statusText: response.statusText,
        });
      },
      onRequestError: ({ error }) => {
        this.logger.error('HTTP请求错误', error);
      },
    });
  }

  /**
   * 发送GET请求
   */
  public async get<T = unknown>(
    url: string,
    options: HttpRequestOptions = {}
  ): Promise<HttpResponse<T>> {
    return this.request<T>(url, { ...options, method: 'GET' });
  }

  /**
   * 发送POST请求
   */
  public async post<T = unknown>(
    url: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- HTTP 请求体格式多样
    body?: BodyInit | Record<string, any> | null,
    options: HttpRequestOptions = {}
  ): Promise<HttpResponse<T>> {
    // 记录原始请求数据
    this.logger.info('📤 发送HTTP请求', {
      url,
      method: 'POST',
      body: typeof body === 'object' ? body : '[非对象数据]',
    });

    const response = await this.request<T>(url, { ...options, method: 'POST', body });

    // 记录原始响应数据
    this.logger.info('📥 收到HTTP响应', {
      url,
      status: response.status,
      data: response.data,
    });

    return response;
  }

  /**
   * 发送PUT请求
   */
  public async put<T = unknown>(
    url: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- HTTP 请求体格式多样
    body?: BodyInit | Record<string, any> | null,
    options: HttpRequestOptions = {}
  ): Promise<HttpResponse<T>> {
    return this.request<T>(url, { ...options, method: 'PUT', body });
  }

  /**
   * 发送DELETE请求
   */
  public async delete<T = unknown>(
    url: string,
    options: HttpRequestOptions = {}
  ): Promise<HttpResponse<T>> {
    return this.request<T>(url, { ...options, method: 'DELETE' });
  }

  /**
   * 发送通用HTTP请求
   */
  public async request<T = unknown>(
    url: string,
    options: HttpRequestOptions = {}
  ): Promise<HttpResponse<T>> {
    const startTime = Date.now();
    const { enableRetry = true, ...fetchOptions } = options;

    try {
      let response: Response;
      let data: T;

      if (enableRetry && this.config.retry.maxAttempts > 1) {
        // 使用重试机制
        response = await this.requestWithRetry(url, fetchOptions);
        data = await response.json();
      } else {
        // 直接请求，确保responseType为json
        const safeOptions = { ...fetchOptions, responseType: 'json' as const };
        data = await this.client<T>(url, safeOptions);
        // 注意：ofetch不直接返回Response对象，需要构造
        response = new Response(JSON.stringify(data), {
          status: 200,
          statusText: 'OK',
        });
      }

      const duration = Date.now() - startTime;
      this.emitEvent('response', { url, status: response.status, duration });

      return {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: this.responseHeadersToObject(response.headers),
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const err = error as Error;

      this.logger.error('HTTP请求失败', {
        url,
        error: err.message,
        duration,
      });

      this.emitEvent('error', { url, error: err, duration });
      throw err;
    }
  }

  /**
   * 带重试的请求
   */
  private async requestWithRetry(url: string, options: FetchOptions): Promise<Response> {
    let lastError: Error | undefined;

    for (let attempt = 1; attempt <= this.config.retry.maxAttempts; attempt++) {
      try {
        // 使用原生fetch进行重试控制
        const { body, ...restOptions } = options;
        const fetchInit: RequestInit = {
          ...restOptions,
          headers: {
            ...this.config.headers,
            ...options.headers,
          },
        };

        // 只有当body不是undefined时才设置
        if (body !== undefined) {
          fetchInit.body =
            typeof body === 'object' && body !== null ? JSON.stringify(body) : (body as BodyInit);
        }

        const response = await fetch(new URL(url, this.config.baseURL).toString(), fetchInit);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response;
      } catch (error) {
        lastError = error as Error;

        this.logger.warn(`HTTP请求失败，尝试 ${attempt}/${this.config.retry.maxAttempts}`, {
          url,
          error: lastError.message,
        });

        // 如果不是最后一次尝试，等待后重试
        if (attempt < this.config.retry.maxAttempts) {
          await this.delay(this.config.retry.delay * attempt);
        }
      }
    }

    // 如果所有重试都失败了，抛出最后一个错误
    if (lastError) {
      throw lastError;
    }
    // 理论上不应该到达这里，但为了类型安全
    throw new Error('请求失败：未知错误');
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 将Response Headers转换为普通对象
   */
  private responseHeadersToObject(headers: Headers): Record<string, string> {
    const result: Record<string, string> = {};
    headers.forEach((value, key) => {
      result[key] = value;
    });
    return result;
  }

  /**
   * 发送事件到事件总线
   */
  private emitEvent<K extends keyof HttpTransportEvents>(
    event: K,
    data: HttpTransportEvents[K]
  ): void {
    this.eventBus.emit(`http:${event}`, data);
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<HttpTransportConfig>): void {
    this.config = { ...this.config, ...newConfig };

    // 重新创建客户端实例
    this.client = ofetch.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: this.config.headers,
    });

    this.logger.info('HTTP传输配置已更新', newConfig);
  }

  /**
   * 获取当前配置
   */
  public getConfig(): HttpTransportConfig {
    return { ...this.config };
  }
}
