/**
 * ASR（语音识别）功能核心测试
 * 测试语音输入启动/停止、语音识别结果处理、收音模式切换
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { testUtils } from '../setup';

describe('ASR语音识别功能测试', () => {
  let mockEventBus: any;
  let mockSDK: any;
  let sessionId: string;

  beforeEach(() => {
    // 创建模拟的EventBus
    mockEventBus = testUtils.createMockEventBus();
    sessionId = testUtils.generateTestId('session');

    // 创建模拟的SDK实例
    mockSDK = {
      getEventBus: () => mockEventBus,
      getStatus: () => ({ isReady: true }),
      generateRequestId: () => testUtils.generateTestId('req'),
      sendJsonRpcMessage: vi.fn().mockResolvedValue({ result: 'success' }),
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('语音输入控制', () => {
    it('应该能够启动语音输入', () => {
      const requestId = mockSDK.generateRequestId();
      let asrStarted: any = null;

      // 注册ASR启动监听器
      mockEventBus.on('asr:start', (data: any) => {
        asrStarted = data;
      });

      // 启动语音输入
      mockEventBus.emit('asr:start', {
        sessionId,
        requestId,
        timestamp: Date.now(),
      });

      // 验证ASR启动事件
      expect(asrStarted.sessionId).toBe(sessionId);
      expect(asrStarted.requestId).toBe(requestId);
    });

    it('应该能够停止语音输入', () => {
      const requestId = mockSDK.generateRequestId();
      let asrStopped: any = null;

      // 注册ASR停止监听器
      mockEventBus.on('asr:stop', (data: any) => {
        asrStopped = data;
      });

      // 停止语音输入
      mockEventBus.emit('asr:stop', {
        sessionId,
        requestId,
        timestamp: Date.now(),
      });

      // 验证ASR停止事件
      expect(asrStopped.sessionId).toBe(sessionId);
      expect(asrStopped.requestId).toBe(requestId);
    });

    it('应该能够处理语音输入超时', () => {
      const requestId = mockSDK.generateRequestId();
      let asrTimeout: any = null;

      // 注册ASR超时监听器
      mockEventBus.on('asr:timeout', (data: any) => {
        asrTimeout = data;
      });

      // 模拟语音输入超时
      mockEventBus.emit('asr:timeout', {
        sessionId,
        requestId,
        timeout: 30000, // 30秒超时
        timestamp: Date.now(),
      });

      // 验证ASR超时事件
      expect(asrTimeout.sessionId).toBe(sessionId);
      expect(asrTimeout.timeout).toBe(30000);
    });
  });

  describe('语音识别结果处理', () => {
    it('应该能够接收语音识别结果', () => {
      const recognizedText = '你好，我想查询账户余额';
      const requestId = mockSDK.generateRequestId();
      let asrResult: any = null;

      // 注册ASR结果监听器
      mockEventBus.on('asr:result', (data: any) => {
        asrResult = data;
      });

      // 模拟语音识别结果
      mockEventBus.emit('asr:result', {
        sessionId,
        requestId,
        text: recognizedText,
        confidence: 0.95,
        timestamp: Date.now(),
      });

      // 验证识别结果
      expect(asrResult.text).toBe(recognizedText);
      expect(asrResult.confidence).toBe(0.95);
      expect(asrResult.sessionId).toBe(sessionId);
    });

    it('应该能够处理部分识别结果', () => {
      const partialTexts = ['你好', '你好我想', '你好我想查询', '你好我想查询账户余额'];
      const requestId = mockSDK.generateRequestId();
      const receivedPartials: string[] = [];

      // 注册部分结果监听器
      mockEventBus.on('asr:partial-result', (data: any) => {
        receivedPartials.push(data.text);
      });

      // 发送部分识别结果
      partialTexts.forEach((text, index) => {
        mockEventBus.emit('asr:partial-result', {
          sessionId,
          requestId,
          text,
          isFinal: index === partialTexts.length - 1,
          timestamp: Date.now(),
        });
      });

      // 验证部分结果
      expect(receivedPartials).toEqual(partialTexts);
    });

    it('应该能够处理语音识别错误', () => {
      const errorMessage = '语音识别服务不可用';
      const requestId = mockSDK.generateRequestId();
      let asrError: any = null;

      // 注册ASR错误监听器
      mockEventBus.on('asr:error', (data: any) => {
        asrError = data;
      });

      // 模拟语音识别错误
      mockEventBus.emit('asr:error', {
        sessionId,
        requestId,
        error: errorMessage,
        errorCode: 'ASR_SERVICE_UNAVAILABLE',
        timestamp: Date.now(),
      });

      // 验证错误处理
      expect(asrError.error).toBe(errorMessage);
      expect(asrError.errorCode).toBe('ASR_SERVICE_UNAVAILABLE');
    });
  });

  describe('收音模式切换', () => {
    it('应该能够切换到唇动收音模式', async () => {
      const requestId = mockSDK.generateRequestId();

      // 模拟发送收音模式切换请求
      const response = await mockSDK.sendJsonRpcMessage({
        jsonrpc: '2.0',
        method: 'speakMode',
        params: {
          speakMode: 0, // 0=唇动收音
        },
        id: requestId,
      });

      // 验证请求被发送
      expect(mockSDK.sendJsonRpcMessage).toHaveBeenCalledWith({
        jsonrpc: '2.0',
        method: 'speakMode',
        params: {
          speakMode: 0,
        },
        id: requestId,
      });

      // 验证响应
      expect(response.result).toBe('success');
    });

    it('应该能够切换到点击收音模式', async () => {
      const requestId = mockSDK.generateRequestId();

      // 模拟发送收音模式切换请求
      const response = await mockSDK.sendJsonRpcMessage({
        jsonrpc: '2.0',
        method: 'speakMode',
        params: {
          speakMode: 1, // 1=点击收音
        },
        id: requestId,
      });

      // 验证请求被发送
      expect(mockSDK.sendJsonRpcMessage).toHaveBeenCalledWith({
        jsonrpc: '2.0',
        method: 'speakMode',
        params: {
          speakMode: 1,
        },
        id: requestId,
      });

      // 验证响应
      expect(response.result).toBe('success');
    });

    it('应该能够处理收音模式切换事件', () => {
      const modes = ['lip', 'button'] as const;
      const receivedModes: string[] = [];

      // 注册收音模式变化监听器
      mockEventBus.on('microphone:mode-changed', (data: any) => {
        receivedModes.push(data.mode);
      });

      // 模拟收音模式变化
      modes.forEach(mode => {
        mockEventBus.emit('microphone:mode-changed', {
          mode,
          sessionId,
          timestamp: Date.now(),
        });
      });

      // 验证模式变化
      expect(receivedModes).toEqual(modes);
    });
  });

  describe('语音状态管理', () => {
    it('应该能够跟踪语音输入状态', () => {
      const states = ['idle', 'listening', 'processing', 'completed'];
      const receivedStates: string[] = [];

      // 注册语音状态监听器
      mockEventBus.on('asr:state-changed', (data: any) => {
        receivedStates.push(data.state);
      });

      // 模拟语音状态变化
      states.forEach(state => {
        mockEventBus.emit('asr:state-changed', {
          state,
          sessionId,
          timestamp: Date.now(),
        });
      });

      // 验证状态变化
      expect(receivedStates).toEqual(states);
    });

    it('应该能够处理语音音量变化', () => {
      const volumes = [0.1, 0.3, 0.7, 0.9, 0.5, 0.2];
      const receivedVolumes: number[] = [];

      // 注册音量变化监听器
      mockEventBus.on('asr:volume-changed', (data: any) => {
        receivedVolumes.push(data.volume);
      });

      // 模拟音量变化
      volumes.forEach(volume => {
        mockEventBus.emit('asr:volume-changed', {
          volume,
          sessionId,
          timestamp: Date.now(),
        });
      });

      // 验证音量变化
      expect(receivedVolumes).toEqual(volumes);
    });
  });

  describe('语音识别配置', () => {
    it('应该能够设置语音识别语言', () => {
      const languages = ['mandarin', 'chuanyu'];
      const receivedLanguages: string[] = [];

      // 注册语言变化监听器
      mockEventBus.on('asr:language-changed', (data: any) => {
        receivedLanguages.push(data.language);
      });

      // 模拟语言变化
      languages.forEach(language => {
        mockEventBus.emit('asr:language-changed', {
          language,
          sessionId,
          timestamp: Date.now(),
        });
      });

      // 验证语言变化
      expect(receivedLanguages).toEqual(languages);
    });

    it('应该能够设置识别敏感度', () => {
      const sensitivity = 0.8;
      let configUpdated: any = null;

      // 注册配置更新监听器
      mockEventBus.on('asr:config-updated', (data: any) => {
        configUpdated = data;
      });

      // 更新识别配置
      mockEventBus.emit('asr:config-updated', {
        sensitivity,
        sessionId,
        timestamp: Date.now(),
      });

      // 验证配置更新
      expect(configUpdated.sensitivity).toBe(sensitivity);
    });
  });
});
