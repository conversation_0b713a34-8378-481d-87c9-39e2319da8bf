/**
 * 业务卡片配置管理
 * 根据不同页面状态配置相应的业务卡片列表
 */

import {
  User,
  Search,
  Smartphone,
  ArrowRightLeft,
  Wallet,
  Target,
  Building,
  CreditCard,
  Receipt,
} from 'lucide-react';
import React from 'react';

import { PageState } from '../core/PageStateManager';

/**
 * 业务卡片接口定义
 */
export interface BusinessCard {
  id: string;
  title: string;
  desc: string;
  icon: React.ComponentType<{ size?: number; style?: React.CSSProperties }>;
  color: string;
}

/**
 * 全部业务卡片定义（完整列表）
 */
const ALL_BUSINESS_CARDS: BusinessCard[] = [
  {
    id: 'AccountManagement',
    title: '账户管理',
    desc: '开卡、挂失、密码管理、账户升降级',
    icon: User,
    color: '#007AFF',
  },
  {
    id: 'AccountInquiry',
    title: '查询业务',
    desc: '账户查询、交易明细、积分服务',
    icon: Search,
    color: '#AF52DE',
  },
  {
    id: 'BusinessContract',
    title: '业务签约',
    desc: '电子渠道签约、银信通、手机支付',
    icon: Smartphone,
    color: '#5AC8FA',
  },
  {
    id: 'Transfer',
    title: '转账汇款',
    desc: '转账、跨行转账、人行通',
    icon: ArrowRightLeft,
    color: '#34C759',
  },
  {
    id: 'SavingsBalance',
    title: '储蓄存款',
    desc: '幸福存、定期存款、大额存单',
    icon: Wallet,
    color: '#FF9500',
  },
  {
    id: 'InvestmentFinancialManagement',
    title: '投资理财',
    desc: '理财产品、基金、风险测评',
    icon: Target,
    color: '#FF3B30',
  },
  {
    id: 'ConvenienceServices',
    title: '政务民生',
    desc: '社保服务、水电气费、通讯费',
    icon: Building,
    color: '#8E8E93',
  },
  {
    id: 'CreditCardBusiness',
    title: '信用卡业务',
    desc: '额度查询、账单查询、自扣还款',
    icon: CreditCard,
    color: '#FF2D92',
  },
  {
    id: 'Cash',
    title: '现金业务',
    desc: '大额存款、小额存款、取款',
    icon: Receipt,
    color: '#34C759',
  },
];

/**
 * 储蓄卡页面业务卡片ID列表（9个业务）
 */
const SAVINGS_CARD_BUSINESS_IDS = [
  'AccountManagement',
  'AccountInquiry',
  'BusinessContract',
  'Transfer',
  'SavingsBalance',
  'InvestmentFinancialManagement',
  'ConvenienceServices',
  'CreditCardBusiness',
  'Cash',
];

/**
 * 信用卡页面业务卡片ID列表（5个业务）
 */
const CREDIT_CARD_BUSINESS_IDS = [
  'AccountManagement',
  'AccountInquiry',
  'CreditCardBusiness',
  'BusinessContract',
  'ConvenienceServices',
];

/**
 * 业务卡片配置管理器
 */
export class BusinessCardConfig {
  /**
   * 根据页面状态获取对应的业务卡片列表
   */
  public static getBusinessCards(pageState: PageState): BusinessCard[] {
    switch (pageState) {
      case 'WaitCardPage':
        // 全功能页面显示所有业务卡片
        return ALL_BUSINESS_CARDS;

      case 'AppSelectAuto':
        // 储蓄卡页面显示指定的9个业务卡片
        return SAVINGS_CARD_BUSINESS_IDS.map(id =>
          ALL_BUSINESS_CARDS.find(card => card.id === id)
        ).filter((card): card is BusinessCard => card !== undefined);

      case 'CreditSelectAuto':
        // 信用卡页面显示指定的5个业务卡片
        return CREDIT_CARD_BUSINESS_IDS.map(id =>
          ALL_BUSINESS_CARDS.find(card => card.id === id)
        ).filter((card): card is BusinessCard => card !== undefined);

      default:
        // 默认返回全部业务卡片
        return ALL_BUSINESS_CARDS;
    }
  }

  /**
   * 获取全部业务卡片
   */
  public static getAllBusinessCards(): BusinessCard[] {
    return ALL_BUSINESS_CARDS;
  }

  /**
   * 根据ID获取业务卡片
   */
  public static getBusinessCardById(id: string): BusinessCard | undefined {
    return ALL_BUSINESS_CARDS.find(card => card.id === id);
  }

  /**
   * 检查指定页面状态是否包含某个业务卡片
   */
  public static hasBusinessCard(pageState: PageState, cardId: string): boolean {
    const cards = this.getBusinessCards(pageState);
    return cards.some(card => card.id === cardId);
  }

  /**
   * 获取页面状态对应的业务卡片数量
   */
  public static getBusinessCardCount(pageState: PageState): number {
    return this.getBusinessCards(pageState).length;
  }
}
