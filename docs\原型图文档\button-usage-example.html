<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银信通按钮系统使用示例</title>
    <link rel="stylesheet" href="button-system.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #f8f9fa;
            padding: 40px;
            line-height: 1.6;
        }
        
        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .demo-title {
            color: #0066cc;
            font-size: 24px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .demo-subtitle {
            color: #333;
            font-size: 18px;
            margin: 25px 0 15px 0;
            font-weight: 500;
        }
        
        .code-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #495057;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; color: #0066cc; margin-bottom: 40px;">银信通按钮系统 - 使用指南</h1>

    <!-- 基础按钮类型 -->
    <div class="demo-section">
        <h2 class="demo-title">1. 基础按钮类型</h2>
        
        <h3 class="demo-subtitle">主要操作按钮（确认、下一步、提交）</h3>
        <div class="btn-group">
            <button class="btn-base btn-primary">确认修改</button>
            <button class="btn-base btn-primary">下一步</button>
            <button class="btn-base btn-primary">提交申请</button>
        </div>
        <div class="code-example">
&lt;button class="btn-base btn-primary"&gt;确认修改&lt;/button&gt;
        </div>

        <h3 class="demo-subtitle">成功操作按钮（同意、确认）</h3>
        <div class="btn-group">
            <button class="btn-base btn-success">我已阅读并同意协议</button>
            <button class="btn-base btn-success">同意并继续</button>
        </div>
        <div class="code-example">
&lt;button class="btn-base btn-success"&gt;我已阅读并同意协议&lt;/button&gt;
        </div>

        <h3 class="demo-subtitle">次要操作按钮（上一步、取消）</h3>
        <div class="btn-group">
            <button class="btn-base btn-secondary">上一步</button>
            <button class="btn-base btn-secondary">取消</button>
            <button class="btn-base btn-secondary">稍后处理</button>
        </div>
        <div class="code-example">
&lt;button class="btn-base btn-secondary"&gt;上一步&lt;/button&gt;
        </div>

        <h3 class="demo-subtitle">导航按钮（返回首页、退出）</h3>
        <div class="btn-group">
            <button class="btn-base btn-nav">返回首页</button>
            <button class="btn-base btn-nav">退出系统</button>
        </div>
        <div class="code-example">
&lt;button class="btn-base btn-nav"&gt;返回首页&lt;/button&gt;
        </div>

        <h3 class="demo-subtitle">警告按钮（重新开始、重置）</h3>
        <div class="btn-group">
            <button class="btn-base btn-warning">重新开始</button>
            <button class="btn-base btn-warning">重置表单</button>
        </div>
        <div class="code-example">
&lt;button class="btn-base btn-warning"&gt;重新开始&lt;/button&gt;
        </div>

        <h3 class="demo-subtitle">危险按钮（删除、清除）</h3>
        <div class="btn-group">
            <button class="btn-base btn-danger">删除信息</button>
            <button class="btn-base btn-danger">清除数据</button>
        </div>
        <div class="code-example">
&lt;button class="btn-base btn-danger"&gt;删除信息&lt;/button&gt;
        </div>
    </div>

    <!-- 银信通页面实际应用示例 -->
    <div class="demo-section">
        <h2 class="demo-title">2. 银信通页面应用示例</h2>

        <h3 class="demo-subtitle">身份证验证页面</h3>
        <div class="btn-group">
            <button class="btn-base btn-nav">返回首页</button>
        </div>
        <div class="code-example">
&lt;!-- 身份证验证页面只需要一个导航按钮 --&gt;
&lt;div class="btn-group"&gt;
    &lt;button class="btn-base btn-nav"&gt;返回首页&lt;/button&gt;
&lt;/div&gt;
        </div>

        <h3 class="demo-subtitle">协议阅读页面</h3>
        <div class="btn-group spread">
            <button class="btn-base btn-nav">返回首页</button>
            <button class="btn-base btn-secondary">上一步</button>
            <button class="btn-base btn-success">我已阅读并同意以上协议</button>
        </div>
        <div class="code-example">
&lt;!-- 协议页面：导航、返回、同意三种操作 --&gt;
&lt;div class="btn-group spread"&gt;
    &lt;button class="btn-base btn-nav"&gt;返回首页&lt;/button&gt;
    &lt;button class="btn-base btn-secondary"&gt;上一步&lt;/button&gt;
    &lt;button class="btn-base btn-success"&gt;我已阅读并同意以上协议&lt;/button&gt;
&lt;/div&gt;
        </div>

        <h3 class="demo-subtitle">信息录入页面</h3>
        <div class="btn-group">
            <button class="btn-base btn-nav">返回首页</button>
            <button class="btn-base btn-secondary">上一步</button>
            <button class="btn-base btn-primary">确认修改</button>
            <button class="btn-base btn-primary">下一步</button>
        </div>
        <div class="code-example">
&lt;!-- 信息录入页面：完整的操作流程 --&gt;
&lt;div class="btn-group"&gt;
    &lt;button class="btn-base btn-nav"&gt;返回首页&lt;/button&gt;
    &lt;button class="btn-base btn-secondary"&gt;上一步&lt;/button&gt;
    &lt;button class="btn-base btn-primary"&gt;确认修改&lt;/button&gt;
    &lt;button class="btn-base btn-primary"&gt;下一步&lt;/button&gt;
&lt;/div&gt;
        </div>
    </div>

    <!-- 按钮尺寸变体 -->
    <div class="demo-section">
        <h2 class="demo-title">3. 按钮尺寸变体</h2>

        <h3 class="demo-subtitle">大尺寸按钮（重要操作）</h3>
        <div class="btn-group">
            <button class="btn-base btn-primary btn-large">开始银信通签约</button>
            <button class="btn-base btn-success btn-large">签约成功完成</button>
        </div>
        <div class="code-example">
&lt;button class="btn-base btn-primary btn-large"&gt;开始银信通签约&lt;/button&gt;
        </div>

        <h3 class="demo-subtitle">标准尺寸按钮（常规操作）</h3>
        <div class="btn-group">
            <button class="btn-base btn-primary">确认</button>
            <button class="btn-base btn-secondary">取消</button>
        </div>

        <h3 class="demo-subtitle">小尺寸按钮（辅助操作）</h3>
        <div class="btn-group">
            <button class="btn-base btn-primary btn-small">编辑</button>
            <button class="btn-base btn-secondary btn-small">查看</button>
            <button class="btn-base btn-warning btn-mini">重置</button>
        </div>
        <div class="code-example">
&lt;button class="btn-base btn-primary btn-small"&gt;编辑&lt;/button&gt;
&lt;button class="btn-base btn-warning btn-mini"&gt;重置&lt;/button&gt;
        </div>
    </div>

    <!-- 特殊状态和效果 -->
    <div class="demo-section">
        <h2 class="demo-title">4. 特殊状态和效果</h2>

        <h3 class="demo-subtitle">禁用状态</h3>
        <div class="btn-group">
            <button class="btn-base btn-primary" disabled>已禁用</button>
            <button class="btn-base btn-secondary" disabled>不可用</button>
        </div>
        <div class="code-example">
&lt;button class="btn-base btn-primary" disabled&gt;已禁用&lt;/button&gt;
        </div>

        <h3 class="demo-subtitle">加载状态</h3>
        <div class="btn-group">
            <button class="btn-base btn-primary btn-loading">处理中...</button>
        </div>
        <div class="code-example">
&lt;button class="btn-base btn-primary btn-loading"&gt;处理中...&lt;/button&gt;
        </div>

        <h3 class="demo-subtitle">脉冲提示效果</h3>
        <div class="btn-group">
            <button class="btn-base btn-success btn-pulse">重要提示</button>
        </div>
        <div class="code-example">
&lt;button class="btn-base btn-success btn-pulse"&gt;重要提示&lt;/button&gt;
        </div>

        <h3 class="demo-subtitle">文字链接按钮</h3>
        <div class="btn-group">
            <button class="btn-base btn-link">查看详情</button>
            <button class="btn-base btn-link">帮助说明</button>
        </div>
        <div class="code-example">
&lt;button class="btn-base btn-link"&gt;查看详情&lt;/button&gt;
        </div>
    </div>

    <!-- 布局规范 -->
    <div class="demo-section">
        <h2 class="demo-title">5. 布局规范</h2>

        <h3 class="demo-subtitle">居中排列（默认）</h3>
        <div class="btn-group">
            <button class="btn-base btn-nav">返回</button>
            <button class="btn-base btn-secondary">上一步</button>
            <button class="btn-base btn-primary">下一步</button>
        </div>

        <h3 class="demo-subtitle">左对齐</h3>
        <div class="btn-group left">
            <button class="btn-base btn-secondary">重置</button>
            <button class="btn-base btn-link">帮助</button>
        </div>

        <h3 class="demo-subtitle">右对齐</h3>
        <div class="btn-group right">
            <button class="btn-base btn-secondary">取消</button>
            <button class="btn-base btn-primary">确认</button>
        </div>

        <h3 class="demo-subtitle">两端对齐</h3>
        <div class="btn-group spread">
            <button class="btn-base btn-nav">返回首页</button>
            <button class="btn-base btn-primary">继续操作</button>
        </div>

        <div class="code-example">
&lt;div class="btn-group"&gt;居中排列（默认）&lt;/div&gt;
&lt;div class="btn-group left"&gt;左对齐&lt;/div&gt;
&lt;div class="btn-group right"&gt;右对齐&lt;/div&gt;
&lt;div class="btn-group spread"&gt;两端对齐&lt;/div&gt;
        </div>
    </div>

    <!-- 设计原则说明 -->
    <div class="demo-section">
        <h2 class="demo-title">6. 设计原则</h2>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; line-height: 1.8;">
            <h4 style="color: #0066cc; margin-bottom: 15px;">颜色语义化：</h4>
            <ul style="margin: 0; padding-left: 20px;">
                <li><strong>蓝色系（Primary）</strong>：主要操作，如确认、下一步、提交</li>
                <li><strong>绿色系（Success）</strong>：成功确认操作，如同意协议、签约成功</li>
                <li><strong>灰色系（Secondary）</strong>：次要操作，如上一步、取消</li>
                <li><strong>蓝绿系（Nav）</strong>：导航操作，如返回首页、退出</li>
                <li><strong>橙色系（Warning）</strong>：警告操作，如重新开始、重置</li>
                <li><strong>红色系（Danger）</strong>：危险操作，如删除、清除</li>
            </ul>
            
            <h4 style="color: #0066cc; margin: 20px 0 15px 0;">交互体验：</h4>
            <ul style="margin: 0; padding-left: 20px;">
                <li>统一的悬浮上升效果增强交互反馈</li>
                <li>圆角设计符合现代UI趋势</li>
                <li>渐变背景提升视觉层次</li>
                <li>禁用状态清晰区分可用性</li>
            </ul>

            <h4 style="color: #0066cc; margin: 20px 0 15px 0;">布局规范：</h4>
            <ul style="margin: 0; padding-left: 20px;">
                <li>使用.btn-group统一按钮间距</li>
                <li>重要操作使用大尺寸按钮</li>
                <li>辅助操作使用小尺寸按钮</li>
                <li>响应式设计适配移动端</li>
            </ul>
        </div>
    </div>
</body>
</html> 