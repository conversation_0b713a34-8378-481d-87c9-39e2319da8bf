/**
 * 路由相关类型定义 - 专注核心路由功能
 */

// 场景类型：两种核心场景
export type ScenarioType = 'custom-component' | 'greeting-page';

// 消息目标类型
export type MessageTarget = 'custom-component' | 'greeting-page';

// 路由状态：当前处于哪种场景
export interface RoutingState {
  currentScenario: ScenarioType;
  sessionId: string;
  isActive: boolean;
}

// 路由决策：ASR和AI响应应该路由到哪里
export interface RoutingDecision {
  target: MessageTarget;
  sessionId: string;
  reason: string;
}

// 路由上下文：用于做路由决策的信息
export interface RoutingContext {
  hasCustomComponent: boolean;
  isGreetingPageVisible: boolean;
  currentSessionId?: string;
  lastActivity?: number;
}

// 路由事件
export interface RoutingChangeEvent {
  from: ScenarioType;
  to: ScenarioType;
  sessionId: string;
  timestamp: number;
}

// 消息路由事件
export interface MessageRouteEvent {
  messageType: 'asr-result' | 'ai-response';
  target: MessageTarget;
  sessionId: string;
  content: string;
}
