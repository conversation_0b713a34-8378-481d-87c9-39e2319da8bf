/**
 * Vitest 测试环境设置
 */

import { vi } from 'vitest';

// 模拟浏览器环境的全局对象
Object.defineProperty(window, 'crypto', {
  value: {
    randomUUID: () => 'test-uuid-' + Math.random().toString(36).substr(2, 9),
    getRandomValues: (arr: Uint8Array) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    },
  },
});

// 模拟 WebSocket
const MockWebSocket = function (this: any, url: string) {
  this.url = url;
  this.addEventListener = vi.fn();
  this.removeEventListener = vi.fn();
  this.send = vi.fn();
  this.close = vi.fn();
  this.readyState = 1;
  return this;
} as any;

// 添加静态常量
MockWebSocket.CONNECTING = 0;
MockWebSocket.OPEN = 1;
MockWebSocket.CLOSING = 2;
MockWebSocket.CLOSED = 3;

global.WebSocket = MockWebSocket;

// 模拟 EventSource
const MockEventSource = function (this: any, url: string) {
  this.url = url;
  this.addEventListener = vi.fn();
  this.removeEventListener = vi.fn();
  this.close = vi.fn();
  this.readyState = 1;
  return this;
} as any;

// 添加静态常量
MockEventSource.CONNECTING = 0;
MockEventSource.OPEN = 1;
MockEventSource.CLOSED = 2;

global.EventSource = MockEventSource;

// 模拟 console 方法（避免测试时的日志输出）
global.console = {
  ...console,
  log: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  debug: vi.fn(),
};

// 添加测试工具函数
export const testUtils = {
  /**
   * 等待指定时间
   */
  wait: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  /**
   * 等待条件满足
   */
  waitFor: async (condition: () => boolean, timeout = 5000, interval = 50) => {
    const start = Date.now();
    while (!condition() && Date.now() - start < timeout) {
      await testUtils.wait(interval);
    }
    if (!condition()) {
      throw new Error(`Condition not met within ${timeout}ms`);
    }
  },

  /**
   * 创建模拟的EventBus
   */
  createMockEventBus: () => {
    const listeners = new Map<string, Function[]>();
    return {
      on: vi.fn((event: string, callback: Function) => {
        if (!listeners.has(event)) {
          listeners.set(event, []);
        }
        listeners.get(event)!.push(callback);
      }),
      off: vi.fn((event: string, callback: Function) => {
        const eventListeners = listeners.get(event);
        if (eventListeners) {
          const index = eventListeners.indexOf(callback);
          if (index > -1) {
            eventListeners.splice(index, 1);
          }
        }
      }),
      emit: vi.fn((event: string, data?: any) => {
        const eventListeners = listeners.get(event);
        if (eventListeners) {
          eventListeners.forEach(callback => callback(data));
        }
      }),
      removeAllListeners: vi.fn(() => {
        listeners.clear();
      }),
      // 测试辅助方法
      _getListeners: () => listeners,
      _hasListener: (event: string) => listeners.has(event) && listeners.get(event)!.length > 0,
    };
  },

  /**
   * 生成测试用的请求ID
   */
  generateTestId: (prefix = 'test') => `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
};
