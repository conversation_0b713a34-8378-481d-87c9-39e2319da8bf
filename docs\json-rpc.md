双向协议（对内）

## 🆕 会话ID自动管理机制

**重要说明**：从当前版本开始，WebSDK实现了智能的会话ID自动管理机制，简化了使用方式：

### 核心特性
1. **完全自动化**：用户无需手动传递sessionId参数，SDK会自动检测当前活跃组件并使用正确的会话ID
2. **智能路由**：消息会自动路由到当前活跃的UI组件（greeting-page或chat-widget）
3. **语言同步**：TTS播报语言与UI显示语言自动保持一致
4. **向后兼容**：即使用户传递了sessionId参数，SDK也会用内部管理的会话ID覆盖，确保系统稳定性

### 活跃组件检测
SDK会自动检测以下信息：
- **当前活跃组件**：greeting-page（打招呼页面）或 chat-widget（聊天组件）
- **真实会话ID**：从MessageRouter获取当前组件的会话ID
- **语言设置**：从localStorage获取当前语言设置（mandarin/chuanyu）

### 使用建议
- **推荐做法**：在所有JSON-RPC请求中省略sessionId参数，让SDK自动管理
- **调试信息**：所有方法的返回值都包含实际使用的sessionId、目标组件、语言等调试信息
- **错误处理**：如果检测到无效的会话状态，SDK会自动创建新的会话

---

## 传输层约定
JSON-RPC对消息体的格式约定，与传输层无关
传输层可以是websocket，可以是JS层两个消息互传消息，可以是HTTP SSE，未来还可以是其他协议。
权限、超时等处理都在传输层完成。

## 消息协议格式
语义层是JSON-RPC 2.0
基础请求格式
JSON
{
  "jsonrpc": "2.0",        //这是保留字段，所有消息都带这个
  "method": "user/info",    //方法名，用/连接
  "params": {                //方法参数JSON
    "userId": 123
  },
  "id": "req_001"         //这是请求id，一个请求唯一对应一个响应
}
基础响应格式
JSON
{
  "jsonrpc": "2.0",          //这是保留字段，所有消息都带这个
  "result": {                //响应格式JSON
    "userId": 123,
    "name": "张三",
    "email": "<EMAIL>"
  },
  "id": "req_001"           //这是响应id，一个请求唯一对应一个响应
}
报错的响应格式
JSON
{
  "jsonrpc": "2.0",
  "error": {        //报错响应格式JSON
    "code": -32603,    //错误码
    "message": "Internal error",
    "data": {
      "partial_result": "你好，我是AI"
    }
  },
  "id": "req_001"  // 错误响应也要匹配原始请求
}
错误码
错误码	错误名称	描述	示例场景
-32700	Parse error	解析错误	JSON格式错误、语法错误
-32600	Invalid Request	无效请求	缺少必需字段、字段类型错误
-32601	Method not found	方法未找到	调用不存在的方法
-32602	Invalid params	无效参数	参数类型错误、缺少必需参数
-32603	Internal error	内部错误	服务器内部处理错误
			
			
事件丢弃机制
•客户端维护pending请求id集合，集合中的请求id对应的响应才会被执行
•当用户跳转新页面或取消操作时，清空pending集合，这样收到之前的响应便可丢弃
•相比之前的pageId的方式更合理，可以过滤相同页面的一些已取消操作
## 🆕 会话机制（自动管理）
**重要更新**：会话ID现在完全由SDK内部自动管理，用户无需关心会话ID的传递和维护。

### 自动管理机制
- **智能检测**：SDK自动检测当前活跃的UI组件（greeting-page或chat-widget）
- **自动分配**：每个组件都有独立的会话ID，SDK会自动使用正确的会话ID
- **参数覆盖**：即使用户传递了sessionId参数，SDK也会用内部管理的会话ID覆盖
- **状态同步**：会话状态与UI组件状态自动保持同步

### 传统机制（仅供参考）
- 背景信息更新、添加消息均需要有对应的会话Id
- 如果不传会话Id，或者会话id为空字符串，AI服务会返回一个新建的uuid
- 当收到了业务操作事件、数据更新事件，也会带有当前会话的id，保留这个会话id，可以在指定会话调用背景更新、添加消息

**注意**：上述传统机制现在由SDK自动处理，用户无需手动管理。
流式响应设计

请求格式
客户端通过标准JSON-RPC请求启用流式响应，无需额外的meta字段
JSON
{
  "jsonrpc": "2.0",
  "method": "api/ai/chat",
  "params": {
    "message": "你好，世界",
    "stream": true              // 可选：显式启用流式响应
  },
  "id": "req_003"
}

流式响应格式
服务端通过notification推送流式内容，使用请求ID进行匹配
JSON
{
  "jsonrpc": "2.0",
  "method": "notifications/chatStreamResponse",
  "params": {
    "message": "你好，",
    "requestId": "req_003",        // 原始请求ID
  }
}

{
  "jsonrpc": "2.0",
  "method": "notifications/chatStreamResponse",
  "params": {
    "message": "我是AI助手，",
    "requestId": "req_003",        // 原始请求ID
  }
}

{
  "jsonrpc": "2.0",
  "method": "notifications/chatStreamResponse",
  "params": {
    "message": "很高兴为您服务！",
    "requestId": "req_003",        // 原始请求ID
  }
}

最终响应
流式响应结束后，发送完整的最终响应
JSON
{
  "jsonrpc": "2.0",
  "id": "req_003",
  "result": {
    "message": "你好，我是AI助手，很高兴为您服务！"
  }
}
服务端反向向客户端请求
JSON
// 获取用户输入
{
  "jsonrpc": "2.0",
  "method": "client/ui/getUserInput",
  "params": {
    "prompt": "请输入您的密码",
    "required": true,
    "timeout": 30000
  },
  "id": "req_003"
}
客户端响应
JSON
// 客户端响应
{
  "jsonrpc": "2.0",
  "result": {
    "value": "123",
    "cancelled": false
  },
  "id": "req_003"
}
报错的相应格式、错误码，与前面相同。

链式UI事件响应设计

当AI需要执行多个UI操作时（如复杂的用户流程），采用链式响应机制
1.通过nextRequestId串联多个响应，形成操作链条
2.客户端通过管理pending列表来控制链式操作的有效性
单步UI操作响应格式
JSON
代码块
{
  "jsonrpc": "2.0",
  "id": "req_001",
  "result": {
    "message": "开始为您注册账号",
    "action": "register",
    "data": {
      "userType": "individual"
    }// 无nextRequestId，操作结束
  }
}
多步UI操作响应格式
第一步响应
JSON
代码块
{
  "jsonrpc": "2.0", 
  "id": "req_001",
  "result": {
    "message": "开始注册流程，正在跳转页面",
    "action": "register",
    "data": {
      "userType": "individual"
    }, 
    "nextRequestId": "req_002"  // 指示下一步的响应ID
  }
}
 第二步响应
JSON
代码块
{
  "jsonrpc": "2.0",
  "id": "req_002", 
  "result": {
    "message": "正在填写用户信息",
    "action": "update",
    "type": "form",
    "data": {
      "name": "用户姓名", 
      "email": "<EMAIL>"
    },
    "nextRequestId": "req_003"
  }
}
第三步响应
JSON
{
  "jsonrpc": "2.0",
  "id": "req_003",
  "result": {
    "message": "注册完成！",
    "action": "submitOrder",
    "data": {
      "confirmRequired": false
    }
    // 无nextRequestId，链条结束
  }
}
客户端处理逻辑
1. 发送消息后，将请求ID加入pending列表
2. 收到响应后，从pending列表移除当前ID，执行UI操作
3. 如果响应包含nextRequestId，将其加入pending列表
4. 当需要取消操作时，清空pending列表，后续响应将被忽略

取消机制：
当用户发送新消息或明确表示取消时，客户端清空pending列表。这样即使服务端后续发送的链式响应到达，也会因为ID不在pending列表中而被忽略，从而实现整个操作链的取消。


具体消息文档
数字人会话界面被调起时，生成sessionId

来自huankestt的事件
huankestt之前与UE建立的是socket通信，发送和接受udp消息 发送端口号48675, 接收端口号10185
现在huankestt与websdk建立的是websocket，huankestt的端口号20096

1.funasr模型初始化成功
JSON
{
    "jsonrpc": "2.0",
    "method": "notifications/modelStatus",
    "params": {
        "loaded": true
    }
}
2.是否有人脸
JSON
{
    "jsonrpc": "2.0",
    "method": "notifications/faceStatus",
    "params": {
        "hasFace": true //false
    }
}
3.ASR识别文本
JSON
{
  "jsonrpc": "2.0",        
  "method": "notifications/asrOfflineResult",    //ASR离线识别结果
  "params": {
        "sid": "[uuid]",    
        "text": "我想改密码"     
  }      
}
4.单轮会话结束
JSON
{
  "jsonrpc": "2.0",        
  "method": "notifications/asrSessionComplete",    //ASR会话结束
  "params": {
  
  }      
}
收到离线识别文本
WebSDK会自动处理ASR识别结果，执行以下操作：

**自动同步机制（v1.0新增功能）：**
当收到 `notifications/asrOfflineResult` 通知时，WebSDK会自动将ASR识别结果同步到用户输入通知系统，确保ASR识别和手动输入使用相同的处理流程。

生成requestId，再转发两个事件
1.1 用户输入事件，发送给客户端（**自动同步**）
JSON
{
  "jsonrpc": "2.0",
  "method": "notifications/userInput",    //用户输入事件（ASR自动同步）
  "params": {
      "userInput":"我想改密码",    //ASR识别的文本内容
      "requestId":"asr-sync-generated-uuid",        //自动生成的UUID请求ID
      "sessionId":"active-component-session-id"        //当前活跃组件的会话ID
  }
}
1.2 请求ai服务器 chat接口
JSON
{
  "jsonrpc": "2.0",        
  "method": "chat",    //用户语音输入事件
  "params": {
      "userInput":"我想改密码",    //用户语音输入的内容
      "sessionId":""        //会话Id      
  },
  "id":"请求id"      
}
看到新交互人
摄像头识别画面，从无人变成有人
向客户软件发送事件
JSON
{
  "jsonrpc": "2.0",        
  "method": "notifications/newUser",    //新用户事件
  "params": {                    
  }      
}
发送控制指令给huankestt
启动收音
JSON
{
  "jsonrpc": "2.0",        
  "method": "startAudio",    //启动收音
  "params": {
    
  },
  "id":"请求id"      
}
完成收音
JSON
{
  "jsonrpc": "2.0",        
  "method": "endAudio",    //完成收音
  "params": {
  },
  "id":"请求id"      
}
丢弃当前收音的会话
JSON
{
  "jsonrpc": "2.0",        
  "method": "stopSession",    //丢弃当前的收音
  "params": {
  },
  "id":"请求id"      
}
控制收音模式
JSON
{
  "jsonrpc": "2.0",        
  "method": "speakMode",    //丢弃当前的收音
  "params": {
      "speakMode":0 //0唇动收音，1点击收音
  },
  "id":"请求id"      
}
与TTS通信
CosyVoice2是什么格式就用什么格式
文本进，音频出
客户软件可能发这些事件来
1. speak事件
让AI播报文字和音频
JSON
{
  "jsonrpc": "2.0",
  "method": "speak",
  "params": {
    "text": "你好，欢迎使用",          //说话内容，编码UTF-8，必填
    "delay": 1000,                   //非负整数，延时delay毫秒数后说话，可选，缺省值为0
    "display": true,                 //是否显示到气泡上，可选，缺省值为true
    // 注意：sessionId参数已由SDK自动管理，无需手动传递
  },
  "id": "req_001"
}
如果display为true，额外增加添加消息事件
添加消息列表到会话历史中
JSON
{
  "jsonrpc": "2.0",
  "method": "addMessages",
  "params": {
    // sessionId由SDK自动管理，无需手动传递
    // "sessionId": "abcd"     // 已废弃：SDK会自动使用当前活跃组件的会话ID
    "messages":  [        //消息列表，必须符合OpenAI的消息格式
       {
          "role":"assistant",
          "content":"speak的内容"
       }
    ]
  },
  "context":{
      "deviceId": "设备id",
      "organizationId":1,
      "locale":"zh-CN"
  },
  "id": "req_002"
}
响应格式
JSON
{
  "jsonrpc": "2.0",
  "result": {
    "sessionId": "auto-managed-session-id"  // SDK自动管理的会话ID
    // 注意：实际响应格式由AI服务器决定，SDK确保使用正确的会话ID
  },
  "id": "req_002"
}
2. 背景信息更新事件
更新页面变化、状态变化等背景信息

### 🆕 页面状态切换功能
支持通过`status`参数实现页面状态切换，支持三种页面状态：
- **WaitCardPage**：全功能页面（显示常用功能区域和所有业务卡片）
- **AppSelectAuto**：储蓄卡业务页面（隐藏常用功能区域，显示储蓄相关业务卡片）
- **CreditSelectAuto**：信用卡业务页面（隐藏常用功能区域，显示信用卡相关业务卡片）

JSON
{
  "jsonrpc": "2.0",
  "method": "updateBackgroundInfo",
  "params": {
    // sessionId由SDK自动管理，无需手动传递
    // "sessionId": "abcd"     // 已废弃：SDK会自动使用当前活跃组件的会话ID
    "page":  "1.2-Greeting-Dialogue-Page",   //当前的页面Id（可选）
    "status": "CreditSelectAuto"              //页面状态切换：WaitCardPage|AppSelectAuto|CreditSelectAuto
  },
  "context":{
      "deviceId": "设备id",
      "organizationId":1,
      "locale":"zh-CN"
  },
  "id": "req_002"
}
响应格式
JSON
{
  "jsonrpc": "2.0",          
  "result": {                
    "sessionId": "abcd"        //更新背景信息所在的会话id
  },
  "id": "req_002"           
}
3. 添加消息事件
添加消息列表到会话历史中，但不触发AI回复
JSON
{
  "jsonrpc": "2.0",        
  "method": "addMessages",    
  "params": {   
    "sessionId": "abcd"     //会话id，如果传的有值，则找到对应会话更新数据，如果没传值，会在响应消息里带上sessionId             
    "messages":  [        //消息列表，必须符合OpenAI的消息格式
       {
          "role":"user 或 assistant",
          "content":"消息内容"
       }
    ] 
  },
  "context":{
      "deviceId": "设备id",
      "organizationId":1,
      "locale":"zh-CN"
  }, 
  "id": "req_002"        
}
响应格式
JSON
{
  "jsonrpc": "2.0",          
  "result": {                
    "sessionId": "abcd"        //更新背景信息所在的会话id
  },
  "id": "req_002"           
}
4. 推送业务数据
推送一些动态数据，比如今天的新闻、在班的工作人员等信息
JSON
{
  "jsonrpc": "2.0",           
  "method": "pushBizData",        //推送业务数据
  "params": {                
    "key": "news",            //业务数据的key值
    "data": {                //业务数据

    },
  },
  "context":{
      "deviceId": "设备id",
      "organizationId":1,
      "locale":"zh-CN"
  }, 
  "id": "req_002"
}
响应格式
JSON
{
  "jsonrpc": "2.0",          
  "result": {                
    "success":  true      //成功只有这个结果，失败会返回error
  },
  "id": "req_002"           
}
WEBSDK向AI服务器发送事件
1. 对话框里输入文本
生成requestId，再转发两个事件
1.1 用户输入事件，发送给客户端
JSON
{
  "jsonrpc": "2.0",        
  "method": "notifications/userInput",    //用户输入事件
  "params": {
      "userInput":"我想改密码",    //用户语音输入的内容
      "requestId":"",        //请求id
      "sessionId":""        //会话Id       
  }      
}
1.2 请求ai服务器 chat接口
JSON
{
  "jsonrpc": "2.0",        
  "method": "chat",    //用户语音输入事件
  "params": {
      "userInput":"我想改密码",    //用户语音输入的内容
      "sessionId":""        //会话Id      
  },
  "id":"请求id"      
}
WEBSDK向客户软件发送事件
1. 业务操作事件
JSON
{
  "jsonrpc": "2.0",
  "id": "req_002",
  "method": "clientUI",   
  "params": {
    "action": "login",
    "data": {
      "type": "idCard"
    }             
  }    
}

{
  "jsonrpc": "2.0",
  "id": "req_002",
  "method": "clientUI",   
  "params": {
    "action": "submitOrder",
    "data": {
      "orderType": "express",
      "confirmRequired": true
    }            
  }    
}

从AI服务器获取，转发给客户端
1. 业务操作事件
来自AI服务器返回的格式：result结构
JSON
{
  "jsonrpc": "2.0",
  "id": "req_002",
  "result": {
    "message": "请先登录您的账号",
    "action": "login",
    "data": {
      "type": "idCard"
    }
  }
}

{
  "jsonrpc": "2.0",
  "id": "req_003", 
  "result": {
    "message": "正在为您提交订单",
    "action": "submitOrder",
    "data": {
      "orderType": "express",
      "confirmRequired": true
    }
  }
}
转发给客户软件
用notification/aiResponse
JSON
{
  "jsonrpc": "2.0",        
  "method": "notifications/aiResponse",   
  "params": {
      "requestId": "req_003",        // 原始请求ID       
      "message": "请先登录您的账号",
      "action": "login",
      "data": {
        "type": "idCard"
      }
  }      
}
2. 数据更新事件
表单填写
只做增量更新，即：只替换有提到的值
JSON
{
  "jsonrpc": "2.0",
  "id": "req_004",
  "result": {
    "message": "正在为您填写用户信息",
    "action": "update",
    "type": "form",
    "data": {
      "name": "李明",
      "phone": "13800138000",
      "gender": "男",
      "age": 25
    }
  }
}
转发给客户软件
JSON
{
  "jsonrpc": "2.0",        
  "method": "notifications/aiResponse",   
  "params": {
      "requestId": "req_004",        // 原始请求ID       
      "message": "正在为您填写用户信息",
      "action": "update",
      "type": "form",
      "data": {
        "name": "李明",
        "phone": "13800138000",
        "gender": "男",
        "age": 25
      }
  }      
}
筛选条件更新
更新全部筛选条件
JSON
{
  "jsonrpc": "2.0",
  "id": "req_005",
  "result": {
    "message": "正在为您更新数据",
    "action": "update",
    "type": "filters",
    "data": {
      "category": "electronics",
      "priceRange": "100-500"
    }
  }
}
转发给客户软件
JSON
{
  "jsonrpc": "2.0",        
  "method": "notifications/aiResponse",   
  "params": {
      "requestId": "req_004",        // 原始请求ID       
      "message": "正在为您更新数据",
      "action": "update",
      "type": "filters",
      "data": {
        "category": "electronics",
        "priceRange": "100-500"
      }
  }      
}
配置更新
只做增量更新，即：只替换有提到的值
JSON
{
  "jsonrpc": "2.0",
  "id": "req_006", 
  "result": {
    "message": "已为您更新系统配置",
    "action": "update",
    "type": "settings",
    "data": {
      "locale": "zh-CN"
    }
  }
}
转发给客户软件
JSON
{
  "jsonrpc": "2.0",        
  "method": "notifications/aiResponse",   
  "params": {
      "requestId": "req_004",        // 原始请求ID       
       "message": "已为您更新系统配置",
      "action": "update",
      "type": "settings",
      "data": {
        "locale": "zh-CN"
      }
  }      
}
3. 中途的状态信息
参数带有requestId，用于标记是哪个请求的中途状态信息
JSON
{
  "jsonrpc": "2.0",        
  "method": "notifications/status",    //通知中途的状态信息
  "params": {
      "requestId": "req_003",        // 原始请求ID       
      "message":"正在分析数据..."     //中途状态信息
  }      
}
这些中途的状态消息，可以处理，也可以不处理，由接收方自行决定。

4. 流式文本消息
这部分转发给TTS端，无需告知客户软件
JSON
{
  "jsonrpc": "2.0",
  "method": "notifications/chatStreamResponse",    //聊天流式返回
  "params": {
      "message":"好的",    //流式返回文本
      "requestId":"[uuid]",        //请求id
      "sessionId":""        //会话Id
  }
}

## ASR识别结果自动同步功能

### 功能概述
WebSDK v1.0 新增了ASR识别结果自动同步到用户输入通知的功能。当收到 `notifications/asrOfflineResult` 通知时，系统会自动将识别到的文本内容同步发送到 `notifications/userInput` 方法，确保ASR识别结果和手动输入使用相同的处理流程。

### 技术实现
1. **监听ASR结果**: `JsonRpcNotificationManager` 监听 `notifications/asrOfflineResult` 事件
2. **提取文本内容**: 从ASR识别结果中提取 `text` 字段
3. **生成请求参数**:
   - `requestId`: 使用UUID格式自动生成
   - `sessionId`: 使用当前活跃UI组件的会话ID（而非ASR会话ID）
   - `userInput`: ASR识别的文本内容
4. **同步调用**: 将参数传递给 `notifications/userInput` 处理逻辑
5. **事件发送**: 同时发送JSON-RPC通知给客户端监听器

### 会话ID管理
- **ASR会话ID**: ASR服务使用的会话标识符
- **UI组件会话ID**: WebSDK内部管理的对话会话标识符
- **自动同步**: 系统自动使用UI组件会话ID，确保与手动输入的会话一致性

### 使用场景
此功能确保无论用户是通过语音输入还是手动输入，都会触发相同的 `notifications/userInput` 事件，使客户端可以统一处理所有用户输入。



