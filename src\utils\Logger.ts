/**
 * 简化的日志工具
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  OFF = 4,
}

export interface LoggerConfig {
  level?: LogLevel;
  prefix?: string;
  enableConsole?: boolean;
}

/**
 * 简单日志器
 */
export class Logger {
  private static instances: Map<string, Logger> = new Map();

  private level: LogLevel;
  private prefix: string;
  private enableConsole: boolean;

  constructor(config: LoggerConfig = {}) {
    this.level = config.level ?? LogLevel.INFO;
    this.prefix = config.prefix ?? 'SDK';
    this.enableConsole = config.enableConsole ?? true;
  }

  /**
   * 获取日志器实例（单例模式）
   */
  static getInstance(config: LoggerConfig = {}): Logger {
    const key = config.prefix || 'default';

    if (!Logger.instances.has(key)) {
      Logger.instances.set(key, new Logger(config));
    }

    const instance = Logger.instances.get(key);
    if (!instance) {
      throw new Error(`Logger实例未找到: ${key}`);
    }
    return instance;
  }

  /**
   * 设置日志级别
   */
  setLevel(level: LogLevel): void {
    this.level = level;
  }

  /**
   * 调试日志
   */
  debug(message: string, data?: unknown): void {
    this.log(LogLevel.DEBUG, message, data);
  }

  /**
   * 信息日志
   */
  info(message: string, data?: unknown): void {
    this.log(LogLevel.INFO, message, data);
  }

  /**
   * 警告日志
   */
  warn(message: string, data?: unknown): void {
    this.log(LogLevel.WARN, message, data);
  }

  /**
   * 错误日志
   */
  error(message: string, data?: unknown): void {
    this.log(LogLevel.ERROR, message, data);
  }

  /**
   * 内部日志方法
   */
  private log(level: LogLevel, message: string, data?: unknown): void {
    if (level < this.level || !this.enableConsole) {
      return;
    }

    const timestamp = new Date().toISOString();
    const levelName = LogLevel[level];
    const logMessage = `[${timestamp}] [${this.prefix}] [${levelName}] ${message}`;

    switch (level) {
      case LogLevel.DEBUG:
        // eslint-disable-next-line no-console -- Logger 工具类需要使用 console
        console.debug(logMessage, data || '');
        break;
      case LogLevel.INFO:
        // eslint-disable-next-line no-console -- Logger 工具类需要使用 console
        console.info(logMessage, data || '');
        break;
      case LogLevel.WARN:
        // eslint-disable-next-line no-console -- Logger 工具类需要使用 console
        console.warn(logMessage, data || '');
        break;
      case LogLevel.ERROR:
        // eslint-disable-next-line no-console -- Logger 工具类需要使用 console
        console.error(logMessage, data || '');
        break;
    }
  }

  /**
   * 清理所有实例
   */
  static clearInstances(): void {
    Logger.instances.clear();
  }
}
