/**
 * 推荐卡片组件
 * 负责显示智能推荐功能
 */

import {
  DollarSign,
  ArrowRightLeft,
  Wallet,
  Star,
  Gift,
  BookOpen,
  Coins,
  Receipt,
} from 'lucide-react';
import React from 'react';

import { Card, CardContent } from '../../components/ui/card';

interface RecommendationItem {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  category: string;
}

interface RecommendationCardsProps {
  onItemClick?: (item: RecommendationItem) => void;
  className?: string;
  style?: React.CSSProperties;
  layoutMode?: 'standard' | 'care'; // 添加布局模式支持
}

export const RecommendationCards: React.FC<RecommendationCardsProps> = ({
  onItemClick,
  className,
  style,
  layoutMode = 'standard', // 默认为标准模式
}) => {
  // 推荐项目数据
  const recommendationItems: RecommendationItem[] = [
    {
      id: 'account-inquiry',
      title: '账户查询',
      description: '查看账户余额信息',
      icon: <Wallet size={layoutMode === 'care' ? 24 : 20} />,
      color: 'linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%)',
      category: '账户服务',
    },
    {
      id: 'transaction-detail',
      title: '交易明细查询',
      description: '查看交易记录明细',
      icon: <ArrowRightLeft size={layoutMode === 'care' ? 24 : 20} />,
      color: 'linear-gradient(135deg, #34C759 0%, #30D158 100%)',
      category: '查询服务',
    },
    {
      id: 'loan-repayment',
      title: '贷款还款查询',
      description: '查询贷款还款记录',
      icon: <DollarSign size={layoutMode === 'care' ? 24 : 20} />,
      color: 'linear-gradient(135deg, #FF9500 0%, #FF8C00 100%)',
      category: '贷款服务',
    },
    {
      id: 'transfer-receipt',
      title: '现金转账交易凭证',
      description: '打印转账凭证',
      icon: <Receipt size={layoutMode === 'care' ? 24 : 20} />,
      color: 'linear-gradient(135deg, #AF52DE 0%, #BF5AF2 100%)',
      category: '凭证服务',
    },
    {
      id: 'points-inquiry',
      title: '积分查询',
      description: '查看积分余额',
      icon: <Coins size={layoutMode === 'care' ? 24 : 20} />,
      color: 'linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%)',
      category: '积分服务',
    },
    {
      id: 'points-consumption',
      title: '积分消费',
      description: '积分兑换商品',
      icon: <Gift size={layoutMode === 'care' ? 24 : 20} />,
      color: 'linear-gradient(135deg, #32D74B 0%, #30B050 100%)',
      category: '积分服务',
    },
    {
      id: 'happy-bean-inquiry',
      title: '幸福豆余额查询',
      description: '查看幸福豆余额',
      icon: <Star size={layoutMode === 'care' ? 24 : 20} />,
      color: 'linear-gradient(135deg, #5856D6 0%, #7B68EE 100%)',
      category: '积分服务',
    },
    {
      id: 'passbook-update',
      title: '存折补登',
      description: '存折补登服务',
      icon: <BookOpen size={layoutMode === 'care' ? 24 : 20} />,
      color: 'linear-gradient(135deg, #FF9F0A 0%, #FFB347 100%)',
      category: '存折服务',
    },
  ];

  const handleItemClick = (item: RecommendationItem) => {
    onItemClick?.(item);
  };

  return (
    <Card
      className={className}
      style={{
        backgroundColor: 'rgba(255, 255, 255, 0.8)', // 改为更不透明的白色背景
        backdropFilter: 'blur(20px) saturate(180%)',
        border: '1px solid rgba(30, 41, 59, 0.1)', // 改为与其他组件一致的边框色
        borderRadius: '20px',
        boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.06)',
        ...style,
      }}
    >
      <div
        style={{
          padding: '20px 20px 16px 20px',
          borderBottom: '1px solid rgba(30, 41, 59, 0.1)', // 改为与其他组件一致的分割线色
        }}
      >
        <h3
          style={{
            fontSize: layoutMode === 'care' ? '28px' : '22px', // 关爱模式28px，普通模式22px（标题文字）
            fontWeight: '700',
            color: '#1e293b',
            margin: 0,
            display: 'flex',
            alignItems: 'center',
            gap: layoutMode === 'care' ? '10px' : '8px', // 关爱模式增加间距
          }}
        >
          <Star size={layoutMode === 'care' ? 24 : 20} style={{ color: '#FFD700' }} />{' '}
          {/* 关爱模式增大图标 */}
          智能推荐
        </h3>
      </div>

      <CardContent style={{ padding: '20px' }}>
        <div
          className="scrollable-area" // 添加滚动条样式类
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: layoutMode === 'care' ? '12px' : '8px', // 关爱模式增加间距
            // 关爱模式显示3条，普通模式显示4条
            maxHeight: layoutMode === 'care' ? '240px' : '320px',
            overflowY: 'auto',
            paddingRight: '8px',
          }}
        >
          {recommendationItems.map(item => (
            <div
              key={item.id}
              onClick={() => handleItemClick(item)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: layoutMode === 'care' ? '16px' : '12px', // 关爱模式增加间距
                padding: layoutMode === 'care' ? '16px 20px' : '12px 16px', // 关爱模式增加内边距
                borderRadius: layoutMode === 'care' ? '12px' : '8px', // 关爱模式增大圆角
                background: 'rgba(255, 255, 255, 0.8)',
                border: '1px solid rgba(30, 41, 59, 0.1)',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
              }}
              onMouseEnter={e => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.95)';
                e.currentTarget.style.transform = 'translateX(4px)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
              }}
              onMouseLeave={e => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.8)';
                e.currentTarget.style.transform = 'translateX(0)';
                e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.05)';
              }}
            >
              <div
                style={{
                  width: layoutMode === 'care' ? '48px' : '40px', // 关爱模式增大图标容器
                  height: layoutMode === 'care' ? '48px' : '40px',
                  borderRadius: layoutMode === 'care' ? '12px' : '8px', // 关爱模式增大圆角
                  background: `${item.color}20`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexShrink: 0,
                }}
              >
                <div
                  style={{ color: item.color.includes('linear-gradient') ? '#007AFF' : item.color }}
                >
                  {item.icon}
                </div>
              </div>
              <div style={{ flex: 1 }}>
                <div
                  style={{
                    fontSize: layoutMode === 'care' ? '24px' : '18px', // 关爱模式24px，普通模式18px（普通文字）
                    fontWeight: '600',
                    color: '#1e293b',
                    marginBottom: layoutMode === 'care' ? '4px' : '2px', // 关爱模式增加间距
                    lineHeight: '1.2',
                  }}
                >
                  {item.title}
                </div>
                <div
                  style={{
                    fontSize: layoutMode === 'care' ? '20px' : '16px', // 关爱模式20px，普通模式16px（次要文字）
                    color: '#64748b',
                    lineHeight: '1.3',
                  }}
                >
                  {item.description}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
