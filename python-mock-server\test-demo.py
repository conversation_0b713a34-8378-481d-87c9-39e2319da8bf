#!/usr/bin/env python3
"""
测试WebSDK Demo的Python脚本
模拟HKSTT发送各种事件到WebSDK
"""

import asyncio
import websockets
import json
import time

class HKSTTSimulator:
    def __init__(self, host='localhost', port=8001):
        self.host = host
        self.port = port
        self.websocket = None
        
    async def connect(self):
        """连接到WebSDK"""
        try:
            uri = f"ws://{self.host}:{self.port}"
            print(f"🔗 连接到WebSDK: {uri}")
            self.websocket = await websockets.connect(uri)
            print("✅ 连接成功")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    async def send_notification(self, method, params=None):
        """发送通知到WebSDK"""
        if not self.websocket:
            print("❌ 未连接到WebSDK")
            return
            
        message = {
            "jsonrpc": "2.0",
            "method": method,
            "params": params or {}
        }
        
        try:
            await self.websocket.send(json.dumps(message))
            print(f"📤 发送: {method}")
            if params:
                print(f"   参数: {params}")
        except Exception as e:
            print(f"❌ 发送失败: {e}")
    
    async def simulate_new_user_flow(self):
        """模拟新用户进入的完整流程"""
        print("\n🎬 开始模拟新用户进入流程...")
        
        # 1. 模拟模型初始化
        print("\n1️⃣ 模拟funasr模型初始化...")
        await self.send_notification("notifications/modelStatus", {
            "status": "initialized",
            "model": "funasr"
        })
        await asyncio.sleep(1)
        
        # 2. 模拟人脸状态：从无人到有人
        print("\n2️⃣ 模拟人脸状态变化...")
        await self.send_notification("notifications/faceStatus", {
            "hasFace": False
        })
        await asyncio.sleep(0.5)
        
        await self.send_notification("notifications/faceStatus", {
            "hasFace": True
        })
        print("   👤 检测到新用户进入")
        await asyncio.sleep(2)
        
        # 3. 模拟用户开始说话
        print("\n3️⃣ 模拟用户语音输入...")
        await self.send_notification("notifications/asrOfflineResult", {
            "sid": f"session_{int(time.time())}",
            "text": "你好，我想查询我的账户余额"
        })
        await asyncio.sleep(1)
        
        # 4. 模拟会话结束
        print("\n4️⃣ 模拟ASR会话结束...")
        await self.send_notification("notifications/asrSessionComplete", {
            "sessionId": f"session_{int(time.time())}"
        })
        
        print("\n✅ 新用户流程模拟完成")
    
    async def simulate_voice_interaction(self):
        """模拟语音交互"""
        print("\n🎤 模拟语音交互...")
        
        voice_inputs = [
            "我想查看我的个人信息",
            "帮我办理业务",
            "谢谢你的帮助"
        ]
        
        for i, text in enumerate(voice_inputs, 1):
            print(f"\n{i}️⃣ 用户说: {text}")
            await self.send_notification("notifications/asrOfflineResult", {
                "sid": f"voice_{int(time.time())}_{i}",
                "text": text
            })
            await asyncio.sleep(2)
            
            await self.send_notification("notifications/asrSessionComplete", {
                "sessionId": f"voice_session_{i}"
            })
            await asyncio.sleep(1)
    
    async def close(self):
        """关闭连接"""
        if self.websocket:
            await self.websocket.close()
            print("🔌 连接已关闭")

async def main():
    """主函数"""
    print("🚀 HKSTT模拟器启动")
    print("📋 功能菜单:")
    print("1. 模拟新用户进入流程")
    print("2. 模拟语音交互")
    print("3. 退出")
    
    simulator = HKSTTSimulator()
    
    # 尝试连接
    if not await simulator.connect():
        print("❌ 无法连接到WebSDK，请确保:")
        print("   1. WebSDK demo页面已打开")
        print("   2. SDK已成功初始化")
        return
    
    try:
        while True:
            print("\n" + "="*50)
            choice = input("请选择操作 (1-3): ").strip()
            
            if choice == "1":
                await simulator.simulate_new_user_flow()
            elif choice == "2":
                await simulator.simulate_voice_interaction()
            elif choice == "3":
                print("👋 退出模拟器")
                break
            else:
                print("❌ 无效选择，请输入1-3")
                
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    finally:
        await simulator.close()

if __name__ == "__main__":
    print("🎯 WebSDK Demo测试工具")
    print("📝 使用说明:")
    print("   1. 确保WebSDK demo页面已在浏览器中打开")
    print("   2. 确保demo页面显示'SDK已连接'状态")
    print("   3. 运行此脚本来模拟HKSTT事件")
    print()
    
    asyncio.run(main())
