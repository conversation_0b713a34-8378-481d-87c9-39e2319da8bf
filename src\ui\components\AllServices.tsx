/**
 * 全部业务区组件
 * 包含业务分类和服务项目列表
 */

import { Grid3X3 } from 'lucide-react';
import React from 'react';

import { Card, CardContent } from '../../components/ui/card';
import { BusinessCardConfig } from '../../config/BusinessCardConfig';
import { PageState } from '../../core/PageStateManager';
import { WebSDK } from '../../core/WebSDK';
import { generateUUID } from '../../utils/helpers';

interface ServiceCategory {
  id: string; // 添加ID字段，用于JSON-RPC事件的action字段
  title: string;
  desc: string;
  icon: React.ComponentType<{ size?: number; style?: React.CSSProperties }>;
  color: string;
}

interface AllServicesProps {
  onServiceClick?: (service: ServiceCategory) => void;
  className?: string;
  style?: React.CSSProperties;
  layoutMode?: 'standard' | 'care'; // 添加布局模式支持
  pageState?: PageState; // 添加页面状态支持
  sdk?: WebSDK; // 添加SDK实例，用于发送事件
}

export const AllServices: React.FC<AllServicesProps> = ({
  onServiceClick,
  className,
  style,
  layoutMode = 'standard', // 默认为标准模式
  pageState = 'WaitCardPage', // 默认为全功能页面
  sdk, // 接收SDK实例
}) => {
  // 根据页面状态获取对应的业务卡片列表
  const businessCards = BusinessCardConfig.getBusinessCards(pageState);

  // 转换为ServiceCategory格式（保持向后兼容）
  const serviceCategories: ServiceCategory[] = businessCards.map(card => ({
    id: card.id,
    title: card.title,
    desc: card.desc,
    icon: card.icon,
    color: card.color,
  }));

  // 确定是否使用关爱模式样式
  // 储蓄卡和信用卡页面使用关爱模式的卡片样式，或者在关爱模式下
  const useCareStyle = layoutMode === 'care' || pageState !== 'WaitCardPage';

  const handleServiceClick = (service: ServiceCategory) => {
    // 调用原有的回调函数
    onServiceClick?.(service);

    // 通过EventBus发送JSON-RPC格式的clientUI事件
    if (sdk && sdk.getStatus().isReady) {
      const eventBus = sdk.getEventBus();
      const requestId = generateUUID();

      const clientUIRequest = {
        jsonrpc: '2.0',
        id: requestId,
        method: 'clientUI',
        params: {
          action: service.id, // 使用业务按钮的ID作为action
          name: service.title, // 添加按钮名称（中文）
        },
      };

      // 发送JSON-RPC格式的clientUI请求（用于内部处理）
      eventBus.emit('client:send-request', clientUIRequest);

      // 发送业务按钮点击事件（用于外部监听）
      eventBus.emit('business:button-click', clientUIRequest);
    }
  };

  return (
    <Card
      className={className}
      style={{
        flex: '3.2', // 适度增加flex值，让全部业务区域占用更多空间但不过度
        background: 'rgba(255, 255, 255, 0.8)',
        backdropFilter: 'blur(20px) saturate(180%)',
        border: '1px solid rgba(30, 41, 59, 0.1)',
        borderRadius: '20px',
        boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.06)',
        ...style,
      }}
    >
      <CardContent style={{ padding: '24px', height: '100%' }}>
        <h3
          style={{
            color: '#1e293b',
            fontSize: useCareStyle ? '24px' : '20px', // 减小字体：关爱样式24px，普通模式20px
            fontWeight: '700',
            marginBottom: useCareStyle ? '12px' : '10px', // 进一步减少下边距以节省空间
            margin: `0 0 ${useCareStyle ? '12px' : '10px'} 0`,
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          <Grid3X3 size={useCareStyle ? 24 : 20} style={{ color: '#34C759' }} />
          全部业务
        </h3>
        <div
          className="scrollable-area" // 添加滚动条样式类
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)', // 固定3列布局，确保在1080p下能完整显示
            gap: useCareStyle ? '12px' : '14px', // 关爱样式减少间距，为内容预留更多空间
            height: `calc(100% - ${useCareStyle ? '35px' : '38px'})`, // 进一步减少预留空间，为更大的卡片提供更多空间
            overflow: 'auto', // 允许滚动以查看所有内容
            alignContent: 'start', // 网格内容从顶部开始对齐
            justifyContent: 'stretch', // 确保卡片填满可用空间
            scrollbarGutter: 'stable', // 始终为滚动条预留空间，防止布局变化
            paddingRight: '4px', // 减少右侧内边距，因为scrollbar-gutter已经预留了空间
          }}
        >
          {serviceCategories.map((item, index) => {
            const IconComponent = item.icon;
            return (
              <Card
                key={index}
                className="hover-lift"
                style={{
                  background: `linear-gradient(135deg, ${item.color}20 0%, ${item.color}10 100%)`,
                  border: `1px solid ${item.color}40`,
                  borderRadius: '14px',
                  cursor: 'pointer',
                  transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                  backdropFilter: 'blur(10px)',
                  minHeight: useCareStyle ? '150px' : '140px', // 大幅增加卡片高度：关爱样式150px，标准模式140px
                  display: 'flex',
                  flexDirection: 'column',
                }}
                onClick={() => handleServiceClick(item)}
                onMouseEnter={e => {
                  e.currentTarget.style.transform = 'translateY(-4px)';
                  e.currentTarget.style.boxShadow = `0 12px 24px ${item.color}30`;
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                <CardContent
                  style={{
                    padding: useCareStyle ? '16px' : '14px', // 增加内边距：关爱样式16px，标准模式14px
                    flex: 1,
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      gap: useCareStyle ? '12px' : '10px', // 增加间距：关爱样式12px，标准模式10px
                      width: '100%', // 确保占满宽度
                    }}
                  >
                    <div
                      style={{
                        padding: useCareStyle ? '8px' : '8px', // 减少内边距
                        borderRadius: '8px', // 减小圆角
                        background: `linear-gradient(135deg, ${item.color} 0%, ${item.color}CC 100%)`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexShrink: 0,
                        width: useCareStyle ? '40px' : '32px', // 增加图标容器：关爱样式40px，标准模式32px
                        height: useCareStyle ? '40px' : '32px', // 增加图标容器
                      }}
                    >
                      <IconComponent
                        size={useCareStyle ? 20 : 18} // 增加图标大小：关爱样式20px，标准模式18px
                        style={{ color: 'white' }}
                      />
                    </div>
                    <div style={{ flex: 1 }}>
                      <h4
                        style={{
                          color: '#1e293b',
                          fontSize: useCareStyle ? '17px' : '16px', // 根据样式模式调整：关爱样式17px，标准模式16px
                          fontWeight: '700',
                          margin: '0 0 4px 0', // 增加下边距
                          lineHeight: '1.3', // 增加行高，提升可读性
                          // 允许标题换行显示，不省略文字
                          wordBreak: 'break-word',
                          whiteSpace: 'normal',
                        }}
                      >
                        {item.title}
                      </h4>
                      <p
                        style={{
                          color: 'rgba(30, 41, 59, 0.7)',
                          fontSize: useCareStyle ? '15px' : '14px', // 根据样式模式调整：关爱样式15px，标准模式14px
                          margin: 0,
                          lineHeight: '1.4', // 增加行高，提升可读性
                          // 防止文字溢出，最多显示3行，为更大的卡片提供更多内容空间
                          overflow: 'hidden',
                          display: '-webkit-box',
                          WebkitLineClamp: 3,
                          WebkitBoxOrient: 'vertical',
                        }}
                      >
                        {item.desc}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};
