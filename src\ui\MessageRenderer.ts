/**
 * MessageRenderer - 消息渲染器
 * 单一职责：只负责消息的显示和渲染
 */

import { html, TemplateResult } from 'lit';

import { ChatWidgetMessage } from '../types/chatwidget';

/**
 * 消息渲染器类
 * 专门负责将消息数据转换为HTML模板
 */
export class MessageRenderer {
  /**
   * 渲染消息列表
   * @param messages 消息数组
   * @returns Lit模板结果
   */
  public renderMessages(messages: ChatWidgetMessage[]): TemplateResult {
    return html`
      <div class="messages-container">${messages.map(message => this.renderMessage(message))}</div>
    `;
  }

  /**
   * 渲染单个消息
   * @param message 消息对象
   * @returns Lit模板结果
   */
  public renderMessage(message: ChatWidgetMessage): TemplateResult {
    const isUser = message.type === 'user';
    const avatarText = isUser ? '我' : 'AI';
    const timestamp = this.formatTimestamp(message.timestamp);

    return html`
      <div class="message ${message.type}" data-message-id="${message.id}">
        ${!isUser ? html` <div class="message-avatar ai-avatar">${avatarText}</div> ` : ''}

        <div class="message-content">
          <div class="message-text">${message.content}</div>
          <div class="message-time">${timestamp}</div>
        </div>

        ${isUser ? html` <div class="message-avatar user-avatar">${avatarText}</div> ` : ''}
      </div>
    `;
  }

  /**
   * 渲染空状态
   * @param welcomeMessage 欢迎消息
   * @returns Lit模板结果
   */
  public renderEmptyState(welcomeMessage?: string): TemplateResult {
    return html`
      <div class="empty-state">
        ${welcomeMessage
          ? html`
              <div class="welcome-message">
                <div class="message ai">
                  <div class="message-avatar ai-avatar">AI</div>
                  <div class="message-content">
                    <div class="message-text">${welcomeMessage}</div>
                    <div class="message-time">${this.formatTimestamp(Date.now())}</div>
                  </div>
                </div>
              </div>
            `
          : html`
              <div class="placeholder">
                <p>开始对话吧！</p>
              </div>
            `}
      </div>
    `;
  }

  /**
   * 渲染加载状态
   * @returns Lit模板结果
   */
  public renderLoadingState(): TemplateResult {
    return html`
      <div class="message ai loading">
        <div class="message-avatar ai-avatar">AI</div>
        <div class="message-content">
          <div class="message-text">
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 格式化时间戳
   * @param timestamp 时间戳
   * @returns 格式化后的时间字符串
   */
  private formatTimestamp(timestamp: number): string {
    const date = new Date(timestamp);
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }

  /**
   * 获取消息样式类名
   * @param message 消息对象
   * @returns CSS类名字符串
   */
  public getMessageClasses(message: ChatWidgetMessage): string {
    const classes = ['message', message.type];

    if (message.status === 'sending') {
      classes.push('sending');
    }

    if (message.status === 'error') {
      classes.push('error');
    }

    if (message.isVoice) {
      classes.push('voice-message');
    }

    return classes.join(' ');
  }
}
