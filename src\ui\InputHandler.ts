/**
 * InputHandler - 输入处理器
 * 单一职责：只负责处理用户输入（文本输入、语音输入）
 */

import { html, TemplateResult } from 'lit';

import { Logger, LogLevel } from '../utils/Logger';

/**
 * 输入事件接口
 */
export interface InputEvents {
  'text:submit': (text: string) => void;
  'voice:start': () => void;
  'voice:end': (text: string) => void;
  'voice:error': (error: Error) => void;
}

/**
 * 输入处理器类
 * 专门负责用户输入的处理和验证
 */
export class InputHandler {
  private logger: Logger;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- 事件系统需要支持不同类型的回调参数
  private eventListeners = new Map<keyof InputEvents, ((...args: any[]) => void)[]>();
  private isVoiceActive = false;
  private currentInputText = '';

  constructor() {
    this.logger = Logger.getInstance({ level: LogLevel.INFO, prefix: 'InputHandler' });
  }

  /**
   * 渲染输入区域
   * @param placeholder 输入框占位符
   * @param disabled 是否禁用输入
   * @returns Lit模板结果
   */
  public renderInputArea(
    placeholder: string = '请输入消息...',
    disabled: boolean = false
  ): TemplateResult {
    return html`
      <div class="input-area">
        <div class="text-input-section">
          <input
            type="text"
            class="text-input"
            placeholder="${placeholder}"
            .value="${this.currentInputText}"
            ?disabled="${disabled}"
            @input="${this.handleTextInput}"
            @keypress="${this.handleKeyPress}"
          />
          <button
            class="send-button"
            ?disabled="${disabled || !this.currentInputText.trim()}"
            @click="${this.handleSendClick}"
            title="发送消息"
          >
            发送
          </button>
        </div>

        <div class="voice-input-section">
          <button
            class="voice-button ${this.isVoiceActive ? 'active' : ''}"
            ?disabled="${disabled}"
            @click="${this.handleVoiceClick}"
            title="${this.isVoiceActive ? '停止录音' : '开始语音输入'}"
          >
            ${this.isVoiceActive ? '停止' : '语音'}
          </button>
        </div>
      </div>
    `;
  }

  /**
   * 渲染简化版输入区域（仅文本输入）
   * @param placeholder 输入框占位符
   * @param disabled 是否禁用输入
   * @returns Lit模板结果
   */
  public renderSimpleInput(
    placeholder: string = '请输入消息...',
    disabled: boolean = false
  ): TemplateResult {
    return html`
      <div class="simple-input-area">
        <input
          type="text"
          class="simple-text-input"
          placeholder="${placeholder}"
          .value="${this.currentInputText}"
          ?disabled="${disabled}"
          @input="${this.handleTextInput}"
          @keypress="${this.handleKeyPress}"
        />
        <button
          class="simple-send-button"
          ?disabled="${disabled || !this.currentInputText.trim()}"
          @click="${this.handleSendClick}"
        >
          发送
        </button>
      </div>
    `;
  }

  /**
   * 处理文本输入
   * @param event 输入事件
   */
  private handleTextInput = (event: Event) => {
    const target = event.target as HTMLInputElement;
    this.currentInputText = target.value;
  };

  /**
   * 处理键盘按键
   * @param event 键盘事件
   */
  private handleKeyPress = (event: KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.submitText();
    }
  };

  /**
   * 处理发送按钮点击
   */
  private handleSendClick = () => {
    this.submitText();
  };

  /**
   * 处理语音按钮点击
   */
  private handleVoiceClick = () => {
    if (this.isVoiceActive) {
      this.stopVoiceInput();
    } else {
      this.startVoiceInput();
    }
  };

  /**
   * 提交文本
   */
  private submitText(): void {
    const text = this.currentInputText.trim();
    if (!text) {
      return;
    }

    // 验证输入
    if (!this.validateInput(text)) {
      return;
    }

    // 添加调用栈追踪
    const stack = new Error().stack;
    this.logger.info('🔍 InputHandler.submitText被调用', {
      text,
      callStack: stack?.split('\n').slice(1, 4).join(' -> '),
    });

    // 触发文本提交事件
    this.emit('text:submit', text);

    // 清空输入框
    this.currentInputText = '';

    this.logger.debug('文本输入提交', { text });
  }

  /**
   * 开始语音输入
   */
  private startVoiceInput(): void {
    this.isVoiceActive = true;
    this.emit('voice:start');

    // 注意：不再模拟语音识别，由SDK的真实ASR功能处理
    // 语音识别结果将通过SDK事件系统传递给ChatWidget
    this.logger.info('开始语音输入，等待SDK ASR结果');
  }

  /**
   * 停止语音输入
   * 公共方法，允许外部调用（如ChatWidget在收到SDK ASR结果后）
   */
  public stopVoiceInput(): void {
    this.isVoiceActive = false;
    this.logger.info('停止语音输入');
  }

  /**
   * 验证输入内容
   * @param text 输入文本
   * @returns 是否有效
   */
  private validateInput(text: string): boolean {
    // 基本验证
    if (text.length > 1000) {
      this.logger.warn('输入文本过长', { length: text.length });
      return false;
    }

    // 可以添加更多验证规则
    return true;
  }

  /**
   * 设置输入文本
   * @param text 文本内容
   */
  public setInputText(text: string): void {
    this.currentInputText = text;
  }

  /**
   * 清空输入
   */
  public clearInput(): void {
    this.currentInputText = '';
    this.logger.debug('InputHandler内部状态已清空');
  }

  /**
   * 获取当前输入文本
   * @returns 当前输入的文本
   */
  public getCurrentText(): string {
    return this.currentInputText;
  }

  /**
   * 是否正在语音输入
   * @returns 语音输入状态
   */
  public isVoiceInputActive(): boolean {
    return this.isVoiceActive;
  }

  /**
   * 事件监听
   */
  public on<K extends keyof InputEvents>(event: K, callback: InputEvents[K]): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.push(callback);
    }
  }

  /**
   * 移除事件监听
   */
  public off<K extends keyof InputEvents>(event: K, callback: InputEvents[K]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  private emit<K extends keyof InputEvents>(event: K, ...args: Parameters<InputEvents[K]>): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          (callback as (...args: unknown[]) => void)(...args);
        } catch (error) {
          this.logger.error(`输入事件处理器错误: ${event}`, error);
        }
      });
    }
  }

  /**
   * 销毁处理器
   */
  public destroy(): void {
    this.eventListeners.clear();
    this.stopVoiceInput();
    this.logger.info('InputHandler 已销毁');
  }
}
