import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
  test: {
    // 测试环境配置
    environment: 'jsdom',

    // 全局设置
    globals: true,

    // TypeScript配置
    typecheck: {
      tsconfig: './tsconfig.test.json'
    },

    // 测试文件匹配模式
    include: [
      'tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'
    ],

    // 排除文件
    exclude: [
      'node_modules',
      'dist',
      '.idea',
      '.git',
      '.cache'
    ],

    // 测试超时设置
    testTimeout: 10000, // 增加到10秒，适合异步ID管理测试
    hookTimeout: 10000,

    // 覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/',
        'dist/',
        'tests/',
        'examples/',
        'docs/',
        'python-mock-server/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/index.ts'
      ],
      // 覆盖率阈值
      thresholds: {
        global: {
          branches: 70,
          functions: 70,
          lines: 70,
          statements: 70
        }
      }
    },

    // 报告器配置
    reporters: ['verbose'],

    // 模拟配置
    mockReset: true,
    clearMocks: true,
    restoreMocks: true,

    // 设置文件
    setupFiles: ['./tests/setup.ts'],

    // 监听模式配置
    watch: false,

    // 并发设置
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false
      }
    }
  },

  // 解析配置
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@/': resolve(__dirname, './src/'),
      '@/types': resolve(__dirname, './src/types'),
      '@/core': resolve(__dirname, './src/core'),
      '@/jsonrpc': resolve(__dirname, './src/jsonrpc'),
      '@/transport': resolve(__dirname, './src/transport'),
      '@/utils': resolve(__dirname, './src/utils'),
      '@/services': resolve(__dirname, './src/services'),
      '@tests': resolve(__dirname, './tests'),
      '@tests/': resolve(__dirname, './tests/'),
    }
  },

  // 定义全局变量
  define: {
    __TEST__: true
  }
});
