/**
 * CosyVoice TTS服务
 * 基于WebSocket的流式语音合成服务
 */

import { EventBus } from '../core/EventBus';
import { generateUUID } from '../utils/helpers';
import { Logger } from '../utils/Logger';

import { StreamingAudioPlayer, PlayerState } from './StreamingAudioPlayer';
import type { ITTSService, TTSStatus, TTSConfig, AudioChunk, TTSSynthesizeParams } from './types';

/**
 * WebSocket消息类型
 */
interface WebSocketMessage {
  type: string;
  params?: Record<string, unknown>;
  timestamp?: string;
}

/**
 * CosyVoice TTS服务实现
 */
export class CosyVoiceTTSService implements ITTSService {
  private eventBus: EventBus;
  private logger: Logger;
  private config: Required<TTSConfig>;
  private websocket: WebSocket | null = null;
  private audioPlayer: StreamingAudioPlayer;
  private currentStatus: TTSStatus = 'idle';
  private clientId: string = '';
  private reconnectAttempts: number = 0;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private connectionTimeout: NodeJS.Timeout | null = null;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private isDestroyed: boolean = false;
  private statusChangeCallbacks: ((status: TTSStatus) => void)[] = [];
  private playStartCallbacks: (() => void)[] = [];
  private playEndCallbacks: (() => void)[] = [];

  constructor(config: TTSConfig, eventBus: EventBus) {
    this.eventBus = eventBus;
    this.logger = Logger.getInstance({
      prefix: 'CosyVoiceTTSService',
    });

    // 设置默认配置
    this.config = {
      serverUrl: config.serverUrl,
      protocol: config.protocol || 'websocket',
      clientIdPrefix: config.clientIdPrefix || 'client_',
      connectionTimeout: config.connectionTimeout || 10000,
      heartbeatInterval: config.heartbeatInterval || 30000,
      maxReconnectAttempts: config.maxReconnectAttempts || 3,
      reconnectDelay: config.reconnectDelay || 3000,
      audioBufferSize: config.audioBufferSize || 1024 * 64,
      requestTimeout: config.requestTimeout || 30000,
      seed: config.seed || 42,
      debug: config.debug || false,
    };

    // 初始化音频播放器
    this.audioPlayer = new StreamingAudioPlayer({
      eventBus: this.eventBus,
      bufferSize: this.config.audioBufferSize,
      debug: this.config.debug,
    });

    this.bindEvents();
    this.generateClientId();
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // 监听播放器状态变化
    this.eventBus.on('tts:player-state-change', (data: unknown) => {
      if (typeof data === 'object' && data !== null && 'state' in data) {
        this.handlePlayerStateChange((data as { state: PlayerState }).state);
      }
    });

    // 监听播放开始
    this.eventBus.on('tts:play-start', () => {
      this.playStartCallbacks.forEach(callback => callback());
      // 轻量化TTS事件：发送DOM事件给数字人组件
      this.dispatchTTSEvent('tts:play-start');
    });

    // 监听播放结束
    this.eventBus.on('tts:play-end', () => {
      this.playEndCallbacks.forEach(callback => callback());
      // 轻量化TTS事件：发送DOM事件给数字人组件
      this.dispatchTTSEvent('tts:play-end');
    });
  }

  /**
   * 生成客户端ID（使用纯UUID）
   */
  private generateClientId(): void {
    this.clientId = generateUUID();
    this.logger.info(`生成客户端ID: ${this.clientId}`);
  }

  /**
   * 连接WebSocket
   */
  private async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const wsUrl = `${this.config.serverUrl}${this.clientId}`;
        this.logger.info(`连接TTS服务器: ${wsUrl}`);

        this.websocket = new WebSocket(wsUrl);
        this.setStatus('connecting');

        // 设置连接超时
        this.connectionTimeout = setTimeout(() => {
          if (this.websocket?.readyState === WebSocket.CONNECTING) {
            this.websocket.close();
            reject(new Error('连接超时'));
          }
        }, this.config.connectionTimeout);

        this.websocket.onopen = () => {
          this.clearConnectionTimeout();
          this.logger.info('WebSocket连接成功');
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.setStatus('idle');
          resolve();
        };

        this.websocket.onmessage = event => {
          this.handleMessage(event.data);
        };

        this.websocket.onclose = event => {
          this.clearConnectionTimeout();
          this.stopHeartbeat();
          this.logger.warn(`WebSocket连接关闭: ${event.code} - ${event.reason}`, {
            reconnectAttempts: this.reconnectAttempts,
            maxReconnectAttempts: this.config.maxReconnectAttempts,
          });

          // 检查是否应该重连
          if (!this.isDestroyed && this.reconnectAttempts < this.config.maxReconnectAttempts) {
            this.scheduleReconnect();
          } else {
            this.logger.error('TTS服务连接失败，已达最大重试次数或服务已销毁', {
              reconnectAttempts: this.reconnectAttempts,
              maxReconnectAttempts: this.config.maxReconnectAttempts,
              isDestroyed: this.isDestroyed,
            });
            this.setStatus('error');
          }
        };

        this.websocket.onerror = error => {
          this.clearConnectionTimeout();
          this.logger.error('WebSocket连接错误', { error });
          this.setStatus('error');
          reject(error);
        };
      } catch (error) {
        this.logger.error('创建WebSocket连接失败', { error });
        reject(error);
      }
    });
  }

  /**
   * 处理WebSocket消息
   */
  private handleMessage(data: string): void {
    try {
      const message = JSON.parse(data);
      this.logger.debug('收到WebSocket消息', { type: message.type });

      switch (message.type) {
        case 'connected':
          this.logger.info('服务器确认连接', { message: message.message });
          break;

        case 'status':
          this.logger.info('状态更新', {
            status: message.status,
            message: message.message,
          });
          if (message.status === 'started') {
            this.setStatus('loading');
            this.audioPlayer.startLoading();
          }
          break;

        case 'audio_chunk':
          this.handleAudioChunk(message);
          break;

        case 'error':
          this.logger.error('服务器错误', { message: message.message });
          this.setStatus('error');
          break;

        case 'pong':
          this.logger.debug('收到心跳响应');
          break;

        default:
          this.logger.warn('未知消息类型', { type: message.type });
      }
    } catch (error) {
      this.logger.error('解析WebSocket消息失败', { error, data });
    }
  }

  /**
   * 处理音频数据块
   */
  private handleAudioChunk(message: {
    chunk_id: string;
    is_final: boolean;
    audio_data: string;
    sample_rate?: number;
    audio_format?: string;
    mime_type?: string;
  }): void {
    try {
      const audioChunk: AudioChunk = {
        chunkId: parseInt(message.chunk_id, 10),
        isFinal: message.is_final,
        audioData: message.audio_data,
        sampleRate: message.sample_rate || 24000,
        audioFormat: message.audio_format || 'wav',
        mimeType: message.mime_type || 'audio/wav',
      };

      this.audioPlayer.addAudioChunk(audioChunk);

      if (audioChunk.isFinal) {
        this.logger.info('音频数据接收完成');
      }
    } catch (error) {
      this.logger.error('处理音频块失败', { error });
      this.setStatus('error');
    }
  }

  /**
   * 处理播放器状态变化
   */
  private handlePlayerStateChange(playerState: PlayerState): void {
    switch (playerState) {
      case PlayerState.PLAYING:
        this.setStatus('playing');
        break;
      case PlayerState.PAUSED:
        this.setStatus('paused');
        break;
      case PlayerState.ENDED:
      case PlayerState.IDLE:
        this.setStatus('idle');
        break;
      case PlayerState.ERROR:
        this.setStatus('error');
        break;
    }
  }

  /**
   * 轻量化TTS事件：发送DOM事件给数字人组件
   */
  private dispatchTTSEvent(eventType: string): void {
    try {
      const event = new CustomEvent(eventType, {
        detail: { timestamp: Date.now(), service: 'CosyVoiceTTS' },
        bubbles: true,
      });
      document.dispatchEvent(event);
      this.logger.debug('发送TTS DOM事件', { eventType });
    } catch (error) {
      this.logger.warn('发送TTS DOM事件失败', { eventType, error });
    }
  }

  /**
   * 发送WebSocket消息
   */
  private sendMessage(message: WebSocketMessage): void {
    if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket未连接');
    }

    this.websocket.send(JSON.stringify(message));
    this.logger.debug('发送WebSocket消息', { type: message.type });
  }

  /**
   * 开始心跳
   */
  private startHeartbeat(): void {
    this.stopHeartbeat();
    this.heartbeatTimer = setInterval(() => {
      if (this.websocket?.readyState === WebSocket.OPEN) {
        this.sendMessage({
          type: 'ping',
          timestamp: new Date().toISOString(),
        });
      }
    }, this.config.heartbeatInterval);
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * 清除连接超时
   */
  private clearConnectionTimeout(): void {
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }
  }

  /**
   * 安排重连（使用指数退避策略）
   */
  private scheduleReconnect(): void {
    if (this.isDestroyed) {
      this.logger.info('服务已销毁，取消重连');
      return;
    }

    this.reconnectAttempts++;

    // 指数退避：基础延迟 * (1.5 ^ 重试次数)，最大30秒
    const baseDelay = this.config.reconnectDelay;
    const exponentialDelay = Math.min(30000, baseDelay * Math.pow(1.5, this.reconnectAttempts - 1));

    this.logger.info(`${exponentialDelay / 1000}秒后进行第${this.reconnectAttempts}次重连`, {
      attempt: this.reconnectAttempts,
      maxAttempts: this.config.maxReconnectAttempts,
      delay: exponentialDelay,
    });

    this.reconnectTimer = setTimeout(() => {
      if (this.isDestroyed) {
        this.logger.info('服务已销毁，取消重连');
        return;
      }

      this.connect().catch(error => {
        this.logger.error('重连失败', {
          error: error.message,
          attempt: this.reconnectAttempts,
          maxAttempts: this.config.maxReconnectAttempts,
        });

        // 如果还没达到最大重试次数，继续重连
        if (this.reconnectAttempts < this.config.maxReconnectAttempts) {
          this.scheduleReconnect();
        } else {
          this.setStatus('error');
        }
      });
    }, exponentialDelay);
  }

  /**
   * 设置状态
   */
  private setStatus(status: TTSStatus): void {
    if (this.currentStatus === status) return;

    this.logger.debug(`TTS状态变化: ${this.currentStatus} -> ${status}`);
    this.currentStatus = status;
    this.statusChangeCallbacks.forEach(callback => callback(status));
  }

  // ITTSService接口实现
  public async speak(text: string, instructText: string = '用自然的声音说话'): Promise<void> {
    try {
      // 只停止播放器，不影响连接
      this.audioPlayer.stop();

      // 确保连接
      if (!this.isConnected()) {
        await this.connect();
      }

      const params: TTSSynthesizeParams = {
        tts_text: text,
        instruct_text: instructText,
      };

      this.sendMessage({
        type: 'synthesize',
        params,
      });

      this.logger.info('开始语音合成', { text: text.substring(0, 50) + '...' });
    } catch (error) {
      this.logger.error('语音合成失败', { error });
      this.setStatus('error');
      throw error;
    }
  }

  public stop(): void {
    this.audioPlayer.stop();

    // 发送播放结束事件（确保数字人停止说话动画）
    this.playEndCallbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        this.logger.error('播放结束回调执行失败', { error });
      }
    });

    // 发送DOM事件给数字人组件
    this.dispatchTTSEvent('tts:play-end');

    // 发送事件总线事件
    this.eventBus.emit('tts:play-end');

    this.setStatus('idle');
  }

  public pause(): void {
    this.audioPlayer.pause();
  }

  public resume(): void {
    this.audioPlayer.resume();
  }

  public onStatusChange(callback: (status: TTSStatus) => void): void {
    this.statusChangeCallbacks.push(callback);
  }

  public onPlayStart(callback: () => void): void {
    this.playStartCallbacks.push(callback);
  }

  public onPlayEnd(callback: () => void): void {
    this.playEndCallbacks.push(callback);
  }

  public getStatus(): TTSStatus {
    return this.currentStatus;
  }

  public isConnected(): boolean {
    return this.websocket?.readyState === WebSocket.OPEN;
  }

  public destroy(): void {
    this.logger.info('销毁TTS服务');
    this.isDestroyed = true;

    // 停止播放
    this.stop();

    // 清理所有定时器
    this.stopHeartbeat();
    this.clearConnectionTimeout();
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    // 关闭WebSocket连接
    if (this.websocket) {
      this.websocket.close(1000, 'Service destroyed');
      this.websocket = null;
    }

    // 销毁音频播放器
    this.audioPlayer.destroy();

    // 清理回调
    this.statusChangeCallbacks = [];
    this.playStartCallbacks = [];
    this.playEndCallbacks = [];

    this.logger.info('TTS服务已销毁');
  }
}
