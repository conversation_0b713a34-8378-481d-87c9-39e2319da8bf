#!/usr/bin/env python3
"""
AI响应适配器单元测试
"""

import unittest
import json
import sys
import os
from datetime import datetime, timedelta

# 添加父目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from adapters.ai_response_adapter import AIResponseAdapter, create_ai_response, validate_jsonrpc_response


class TestAIResponseAdapter(unittest.TestCase):
    """AI响应适配器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.adapter = AIResponseAdapter()
        self.request_id = "req_test_001"
        self.session_id = "session_test_123"
    
    def tearDown(self):
        """测试后清理"""
        self.adapter.clear_all_contexts()
    
    def test_register_request(self):
        """测试请求注册"""
        self.adapter.register_request(self.request_id, self.session_id)
        
        status = self.adapter.get_status()
        self.assertEqual(status["active_contexts"], 1)
        
        # 检查上下文是否正确创建
        context = self.adapter.request_contexts[self.request_id]
        self.assertEqual(context.request_id, self.request_id)
        self.assertEqual(context.session_id, self.session_id)
        self.assertEqual(context.status, "active")
    
    def test_adapt_streaming_response(self):
        """测试流式响应适配"""
        # 创建AI服务器流式响应
        ai_response = create_ai_response(2, "你好，", self.session_id)
        
        # 适配响应
        result = self.adapter.adapt_response(ai_response, self.request_id)
        
        # 验证结果
        self.assertIsNotNone(result)
        if result is not None:  # 类型守卫
            self.assertEqual(result["jsonrpc"], "2.0")
            self.assertEqual(result["method"], "notifications/progress")
            self.assertEqual(result["params"]["message"], "你好，")
            self.assertEqual(result["params"]["requestId"], self.request_id)
            self.assertEqual(result["params"]["sessionId"], self.session_id)

            # 验证JSON-RPC格式
            self.assertTrue(validate_jsonrpc_response(result))
    
    def test_adapt_final_response(self):
        """测试最终响应适配"""
        # 先发送几个流式响应
        ai_response1 = create_ai_response(2, "你好，", self.session_id)
        ai_response2 = create_ai_response(2, "我是AI助手。", self.session_id)
        
        self.adapter.adapt_response(ai_response1, self.request_id)
        self.adapter.adapt_response(ai_response2, self.request_id)
        
        # 发送最终响应（空消息，这样会使用缓冲的消息）
        ai_response_final = create_ai_response(0, "", self.session_id)
        result = self.adapter.adapt_response(ai_response_final, self.request_id)

        # 验证结果
        self.assertIsNotNone(result)
        if result is not None:  # 类型守卫
            self.assertEqual(result["jsonrpc"], "2.0")
            self.assertIn("id", result)
            self.assertEqual(result["id"], self.request_id)
            self.assertIn("result", result)

            # 验证消息合并
            expected_message = "你好，我是AI助手。"  # 缓冲的消息
            self.assertEqual(result["result"]["message"], expected_message)
            self.assertEqual(result["result"]["sessionId"], self.session_id)
        
        # 验证上下文已清理
        status = self.adapter.get_status()
        self.assertEqual(status["active_contexts"], 0)
    
    def test_adapt_error_response(self):
        """测试错误响应适配"""
        # 创建错误响应
        ai_response = {
            "code": 1,
            "data": {
                "sessionId": self.session_id,
                "source": 6,
                "message": "",
                "extra": None
            },
            "errorMsg": "服务器内部错误",
            "language": "zh-CN"
        }
        
        result = self.adapter.adapt_response(ai_response, self.request_id)
        
        # 验证结果
        self.assertIsNotNone(result)
        if result is not None:  # 类型守卫
            self.assertEqual(result["jsonrpc"], "2.0")
            self.assertEqual(result["id"], self.request_id)
            self.assertIn("error", result)
            self.assertEqual(result["error"]["code"], -32603)
            self.assertEqual(result["error"]["message"], "服务器内部错误")
        
        # 验证上下文已清理
        status = self.adapter.get_status()
        self.assertEqual(status["active_contexts"], 0)
    
    def test_filter_intermediate_response(self):
        """测试中间状态响应过滤"""
        # 创建中间状态响应（code=1且无错误消息）
        ai_response = create_ai_response(1, "中间状态消息", self.session_id)
        
        result = self.adapter.adapt_response(ai_response, self.request_id)
        
        # 应该返回None（被过滤）
        self.assertIsNone(result)
    
    def test_message_buffering(self):
        """测试消息缓冲功能"""
        messages = ["第一部分", "第二部分", "第三部分"]
        
        # 发送多个流式响应
        for msg in messages:
            ai_response = create_ai_response(2, msg, self.session_id)
            result = self.adapter.adapt_response(ai_response, self.request_id)
            self.assertIsNotNone(result)
            if result is not None:  # 类型守卫
                self.assertEqual(result["params"]["message"], msg)
        
        # 发送最终响应
        ai_response_final = create_ai_response(0, "", self.session_id)
        result = self.adapter.adapt_response(ai_response_final, self.request_id)
        
        # 验证消息合并
        if result is not None:  # 类型守卫
            expected_message = "".join(messages)
            self.assertEqual(result["result"]["message"], expected_message)
    
    def test_buffer_size_limit(self):
        """测试缓冲区大小限制"""
        # 设置较小的缓冲区大小用于测试
        self.adapter.max_buffer_size = 3
        
        # 发送超过缓冲区大小的消息
        messages = [f"消息{i}" for i in range(5)]
        
        for msg in messages:
            ai_response = create_ai_response(2, msg, self.session_id)
            self.adapter.adapt_response(ai_response, self.request_id)
        
        # 检查缓冲区大小
        context = self.adapter.request_contexts[self.request_id]
        self.assertEqual(len(context.message_buffer), 3)  # 应该只保留最后3个
        self.assertEqual(context.message_buffer, ["消息2", "消息3", "消息4"])
    
    def test_context_auto_creation(self):
        """测试上下文自动创建"""
        # 不预先注册请求，直接适配响应
        ai_response = create_ai_response(2, "测试消息", self.session_id)
        result = self.adapter.adapt_response(ai_response, self.request_id)
        
        # 验证上下文自动创建
        self.assertIsNotNone(result)
        self.assertIn(self.request_id, self.adapter.request_contexts)
        
        context = self.adapter.request_contexts[self.request_id]
        self.assertEqual(context.session_id, self.session_id)
    
    def test_cleanup_expired_contexts(self):
        """测试过期上下文清理"""
        # 创建一些上下文
        self.adapter.register_request("req1", "session1")
        self.adapter.register_request("req2", "session2")
        
        # 手动设置一个上下文为过期
        old_time = datetime.now() - timedelta(seconds=400)  # 超过300秒超时
        self.adapter.request_contexts["req1"].start_time = old_time
        
        # 清理过期上下文
        cleaned_count = self.adapter.cleanup_expired_contexts()
        
        self.assertEqual(cleaned_count, 1)
        self.assertNotIn("req1", self.adapter.request_contexts)
        self.assertIn("req2", self.adapter.request_contexts)
    
    def test_get_status(self):
        """测试状态获取"""
        # 创建一些上下文
        self.adapter.register_request("req1", "session1")
        self.adapter.register_request("req2", "session2")
        
        status = self.adapter.get_status()
        
        self.assertEqual(status["active_contexts"], 2)
        self.assertGreaterEqual(status["oldest_context_age"], 0)
        self.assertEqual(status["max_buffer_size"], self.adapter.max_buffer_size)
        self.assertEqual(status["context_timeout"], self.adapter.context_timeout)
    
    def test_clear_all_contexts(self):
        """测试清理所有上下文"""
        # 创建一些上下文
        self.adapter.register_request("req1", "session1")
        self.adapter.register_request("req2", "session2")
        
        cleared_count = self.adapter.clear_all_contexts()
        
        self.assertEqual(cleared_count, 2)
        self.assertEqual(len(self.adapter.request_contexts), 0)
    
    def test_validate_ai_response(self):
        """测试AI响应验证"""
        # 有效响应
        valid_response = create_ai_response(2, "测试", "session123")
        self.assertTrue(self.adapter.validate_ai_response(valid_response))
        
        # 无效响应 - 缺少code字段
        invalid_response1 = {"data": {"message": "test"}}
        self.assertFalse(self.adapter.validate_ai_response(invalid_response1))
        
        # 无效响应 - 缺少data字段
        invalid_response2 = {"code": 2}
        self.assertFalse(self.adapter.validate_ai_response(invalid_response2))
        
        # 无效响应 - code值无效
        invalid_response3 = create_ai_response(5, "测试", "session123")
        self.assertFalse(self.adapter.validate_ai_response(invalid_response3))
    
    def test_create_test_response(self):
        """测试创建测试响应"""
        # 创建流式响应
        streaming_response = self.adapter.create_test_response(self.request_id, "测试消息", False)
        if streaming_response is not None:  # 类型守卫
            self.assertEqual(streaming_response["method"], "notifications/progress")
            self.assertEqual(streaming_response["params"]["message"], "测试消息")

        # 创建最终响应
        final_response = self.adapter.create_test_response(self.request_id, "最终消息", True)
        if final_response is not None:  # 类型守卫
            self.assertIn("result", final_response)
            self.assertEqual(final_response["result"]["message"], "最终消息")
    
    def test_concurrent_requests(self):
        """测试并发请求处理"""
        request_ids = ["req1", "req2", "req3"]
        session_ids = ["session1", "session2", "session3"]
        
        # 并发注册请求
        for req_id, sess_id in zip(request_ids, session_ids):
            self.adapter.register_request(req_id, sess_id)
        
        # 并发处理响应
        for i, (req_id, sess_id) in enumerate(zip(request_ids, session_ids)):
            ai_response = create_ai_response(2, f"消息{i}", sess_id)
            result = self.adapter.adapt_response(ai_response, req_id)
            
            self.assertIsNotNone(result)
            if result is not None:  # 类型守卫
                self.assertEqual(result["params"]["requestId"], req_id)
                self.assertEqual(result["params"]["sessionId"], sess_id)
        
        # 验证所有上下文都存在
        self.assertEqual(len(self.adapter.request_contexts), 3)


class TestUtilityFunctions(unittest.TestCase):
    """工具函数测试类"""
    
    def test_create_ai_response(self):
        """测试创建AI响应"""
        response = create_ai_response(2, "测试消息", "session123", "")
        
        self.assertEqual(response["code"], 2)
        self.assertEqual(response["data"]["message"], "测试消息")
        self.assertEqual(response["data"]["sessionId"], "session123")
        self.assertEqual(response["errorMsg"], "")
        self.assertEqual(response["language"], "zh-CN")
    
    def test_validate_jsonrpc_response(self):
        """测试JSON-RPC响应验证"""
        # 有效的通知
        valid_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/progress",
            "params": {"message": "test"}
        }
        self.assertTrue(validate_jsonrpc_response(valid_notification))
        
        # 有效的响应
        valid_response = {
            "jsonrpc": "2.0",
            "id": "req1",
            "result": {"message": "test"}
        }
        self.assertTrue(validate_jsonrpc_response(valid_response))
        
        # 有效的错误响应
        valid_error = {
            "jsonrpc": "2.0",
            "id": "req1",
            "error": {"code": -32603, "message": "Internal error"}
        }
        self.assertTrue(validate_jsonrpc_response(valid_error))
        
        # 无效响应 - 缺少jsonrpc字段
        invalid_response1 = {"method": "test", "params": {}}
        self.assertFalse(validate_jsonrpc_response(invalid_response1))
        
        # 无效响应 - jsonrpc版本错误
        invalid_response2 = {"jsonrpc": "1.0", "method": "test", "params": {}}
        self.assertFalse(validate_jsonrpc_response(invalid_response2))
        
        # 无效响应 - 通知包含id字段
        invalid_notification = {
            "jsonrpc": "2.0",
            "method": "test",
            "params": {},
            "id": "req1"
        }
        self.assertFalse(validate_jsonrpc_response(invalid_notification))


if __name__ == '__main__':
    unittest.main()
