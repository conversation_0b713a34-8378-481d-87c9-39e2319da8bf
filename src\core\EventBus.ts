/**
 * 事件总线
 * 用于SDK内部模块间通信和外部事件监听
 */

// 简单的事件回调类型
type EventCallback = (data?: unknown) => void;

/**
 * 事件总线类
 */
export class EventBus {
  private events: Map<string, EventCallback[]> = new Map();
  private instanceId = Math.random().toString(36).substring(2, 11);

  /**
   * 注册事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  public on(event: string, callback: EventCallback): void {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }

    const callbacks = this.events.get(event) || [];
    callbacks.push(callback);

    // 调试日志已移除，避免生产环境输出
  }

  /**
   * 移除事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  public off(event: string, callback: EventCallback): void {
    if (!this.events.has(event)) {
      // 静默处理不存在的事件移除
      return;
    }

    const callbacks = this.events.get(event) || [];
    const index = callbacks.indexOf(callback);

    if (index !== -1) {
      callbacks.splice(index, 1);
      // 监听器移除成功
    } else {
      // 未找到要移除的监听器
    }
  }

  /**
   * 触发事件
   * @param event 事件名称
   * @param data 事件数据
   */
  public emit(event: string, data?: unknown): void {
    if (!this.events.has(event)) {
      return;
    }

    const callbacks = this.events.get(event) || [];

    // 如果监听器数量大于1，输出警告（特别是关键事件）
    if (callbacks.length > 1) {
      const criticalEvents = ['chatStreamResponse', 'userInput', 'asrOfflineResult'];
      const isCritical = criticalEvents.some(critical => event.includes(critical));

      if (isCritical) {
        // 关键事件多监听器警告已移除，避免生产环境输出
        // 可以考虑使用专门的日志系统记录此类警告
      }
    }

    for (const callback of callbacks) {
      try {
        callback(data);
      } catch {
        // 事件处理器错误静默处理，避免生产环境输出
        // 可以考虑使用专门的错误处理系统
      }
    }
  }

  /**
   * 清除所有事件监听
   */
  public clear(): void {
    this.events.clear();
  }

  /**
   * 清除特定事件的所有监听
   * @param event 事件名称
   */
  public clearEvent(event: string): void {
    this.events.delete(event);
  }

  /**
   * 获取事件监听器数量
   * @param event 事件名称
   * @returns 监听器数量
   */
  public listenerCount(event: string): number {
    return this.events.has(event) ? (this.events.get(event) || []).length : 0;
  }

  /**
   * 检查是否有特定事件的监听器
   * @param event 事件名称
   * @returns 是否有监听器
   */
  public hasListeners(event: string): boolean {
    return this.listenerCount(event) > 0;
  }

  /**
   * 获取EventBus实例ID
   * @returns 实例ID
   */
  public getInstanceId(): string {
    return this.instanceId;
  }
}
