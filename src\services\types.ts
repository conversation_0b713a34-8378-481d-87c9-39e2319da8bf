/**
 * 共享服务类型定义
 */

// 消息类型
export interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: number;
  status: 'sending' | 'sent' | 'received' | 'error';
  sessionId: string;
  isStreaming?: boolean;
  isComplete?: boolean;
}

// 消息服务接口
export interface IMessageService {
  // 发送用户消息
  sendUserMessage(content: string, sessionId?: string): Promise<void>;

  // 监听AI响应
  onAIResponse(callback: (message: ChatMessage) => void): void;

  // 监听消息状态变化
  onMessageStatusChange(callback: (message: ChatMessage) => void): void;

  // 获取消息历史
  getMessageHistory(sessionId: string): ChatMessage[];

  // 清空消息历史
  clearHistory(sessionId?: string): void;

  // 销毁服务
  destroy(): void;
}

// ASR状态
export type ASRStatus = 'idle' | 'connecting' | 'connected' | 'listening' | 'processing' | 'error';

// ASR服务接口
export interface IASRService {
  // 开始语音识别
  startRecognition(): Promise<void>;

  // 停止语音识别
  stopRecognition(): void;

  // 监听识别结果
  onRecognitionResult(callback: (text: string, isComplete: boolean) => void): void;

  // 监听识别状态
  onStatusChange(callback: (status: ASRStatus) => void): void;

  // 获取当前状态
  getStatus(): ASRStatus;

  // 销毁服务
  destroy(): void;
}

// TTS状态
export type TTSStatus = 'idle' | 'connecting' | 'loading' | 'playing' | 'paused' | 'error';

// TTS配置接口（兼容WebSocket和HTTP+SSE）
export interface TTSConfig {
  /** 服务器URL（WebSocket或HTTP） */
  serverUrl: string;
  /** 协议类型 */
  protocol?: 'websocket' | 'http-sse';
  /** 客户端ID前缀（仅WebSocket） */
  clientIdPrefix?: string;
  /** 连接超时时间(ms) */
  connectionTimeout?: number;
  /** 心跳间隔(ms)（仅WebSocket） */
  heartbeatInterval?: number;
  /** 最大重连次数 */
  maxReconnectAttempts?: number;
  /** 重连延迟(ms) */
  reconnectDelay?: number;
  /** 音频缓冲大小 */
  audioBufferSize?: number;
  /** 请求超时时间(ms)（仅HTTP+SSE） */
  requestTimeout?: number;
  /** 固定随机种子（仅HTTP+SSE） */
  seed?: number;
  /** 是否启用调试日志 */
  debug?: boolean;
}

// 音频数据块接口
export interface AudioChunk {
  chunkId: number;
  isFinal: boolean;
  audioData: string; // Base64编码
  sampleRate: number;
  audioFormat: string;
  mimeType: string;
}

// TTS合成参数
export interface TTSSynthesizeParams extends Record<string, unknown> {
  /** 要合成的文本 */
  tts_text: string;
  /** 指令文本，控制语音特征 */
  instruct_text: string;
}

// TTS服务接口
export interface ITTSService {
  // 播放文本
  speak(text: string, instructText?: string): Promise<void>;

  // 停止播放
  stop(): void;

  // 暂停播放
  pause(): void;

  // 继续播放
  resume(): void;

  // 监听播放状态
  onStatusChange(callback: (status: TTSStatus) => void): void;

  // 监听播放开始
  onPlayStart(callback: () => void): void;

  // 监听播放结束
  onPlayEnd(callback: () => void): void;

  // 获取当前状态
  getStatus(): TTSStatus;

  // 检查连接状态
  isConnected(): boolean;

  // 销毁服务
  destroy(): void;
}

// 会话服务接口
export interface ISessionService {
  // 创建新会话
  createSession(): string;

  // 获取当前会话
  getCurrentSession(): string | null;

  // 切换会话
  switchSession(sessionId: string): void;

  // 结束会话
  endSession(sessionId: string): void;

  // 监听会话变化
  onSessionChange(callback: (sessionId: string | null) => void): void;

  // 销毁服务
  destroy(): void;
}

// 服务配置
export interface ServiceConfig {
  // 消息服务配置
  message?: {
    maxHistorySize?: number;
    enablePersistence?: boolean;
  };

  // ASR服务配置
  asr?: {
    autoStart?: boolean;
    language?: string;
  };

  // TTS服务配置
  tts?: {
    voice?: string;
    rate?: number;
    volume?: number;
  };

  // 会话服务配置
  session?: {
    autoCreate?: boolean;
    sessionTimeout?: number;
  };
}

// 服务容器接口
export interface IServiceContainer {
  // 注册服务
  register<T>(name: string, service: T): void;

  // 获取服务
  get<T>(name: string): T | null;

  // 检查服务是否存在
  has(name: string): boolean;

  // 销毁所有服务
  destroy(): void;
}

// 事件回调类型
export type EventCallback<T = unknown> = (data: T) => void;

// 事件取消订阅函数
export type UnsubscribeFunction = () => void;
