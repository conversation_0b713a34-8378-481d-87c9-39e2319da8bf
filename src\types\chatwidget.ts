/**
 * 聊天组件类型定义 - 极简版本
 */

// 聊天消息
export interface ChatWidgetMessage {
  id: string;
  role: 'user' | 'assistant';
  type: 'user' | 'assistant'; // 兼容旧代码
  content: string;
  timestamp: number;
  status?: 'sending' | 'sent' | 'error';
  isVoice?: boolean;
}

// 聊天组件配置
export interface ChatWidgetConfig {
  title?: string;
  placeholder?: string;
  maxMessages?: number;
  autoScroll?: boolean;
}

// 聊天组件状态
export interface ChatWidgetState {
  messages: ChatWidgetMessage[];
  isLoading: boolean;
  isConnected: boolean;
}

// 聊天组件事件
export interface ChatWidgetEvents {
  'message-sent': { message: string };
  'message-received': { message: ChatWidgetMessage };
  'state-changed': { state: ChatWidgetState };
}
