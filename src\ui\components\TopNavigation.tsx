/**
 * 顶部导航栏组件
 * 包含返回按钮、标题和徽章
 */

import { Home, Mic, MicOff, Volume2, Users, Settings } from 'lucide-react';
import React from 'react';

import { Badge } from '../../components/ui/badge';

interface TopNavigationProps {
  onBack?: () => void;
  className?: string;
  style?: React.CSSProperties;
  microphoneMode?: 'button' | 'lip';
  onMicrophoneModeChange?: (mode: 'button' | 'lip') => void;
  language?: 'mandarin' | 'chuanyu';
  onLanguageChange?: (language: 'mandarin' | 'chuanyu') => void;
  layoutMode?: 'standard' | 'care';
  onLayoutModeChange?: (mode: 'standard' | 'care') => void;
}

interface ToggleState {
  audioMode: 'button' | 'lip'; // 按键收音/唇动收音
  language: 'chuanyu' | 'mandarin'; // 川渝话/普通话
  mode: 'standard' | 'care'; // 标准模式/关爱模式
}

export const TopNavigation: React.FC<TopNavigationProps> = ({
  onBack,
  className,
  style,
  microphoneMode = 'button',
  onMicrophoneModeChange,
  language = 'mandarin',
  onLanguageChange,
  layoutMode = 'standard',
  onLayoutModeChange,
}) => {
  const [toggleState, setToggleState] = React.useState<ToggleState>({
    audioMode: microphoneMode,
    language: language,
    mode: layoutMode,
  });

  // 同步外部传入的状态
  React.useEffect(() => {
    setToggleState(prev => ({
      ...prev,
      audioMode: microphoneMode,
      language: language,
      mode: layoutMode,
    }));
  }, [microphoneMode, language, layoutMode]);

  const handleToggle = (type: keyof ToggleState) => {
    if (type === 'audioMode') {
      const newMode = toggleState.audioMode === 'button' ? 'lip' : 'button';
      onMicrophoneModeChange?.(newMode);
    } else if (type === 'language') {
      const newLanguage = toggleState.language === 'chuanyu' ? 'mandarin' : 'chuanyu';
      onLanguageChange?.(newLanguage);
    } else if (type === 'mode') {
      const newMode = toggleState.mode === 'standard' ? 'care' : 'standard';
      onLayoutModeChange?.(newMode);
    }
  };

  return (
    <div
      className={className}
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '12px 24px',
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(20px) saturate(180%)',
        borderBottom: '1px solid rgba(30, 41, 59, 0.1)',
        height: '64px',
        ...style,
      }}
    >
      {/* 左侧返回按钮 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <button
          onClick={onBack}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px', // 增加间距
            padding: '12px 16px', // 增大内边距
            borderRadius: '10px', // 增大圆角
            fontSize: '18px', // 统一普通文字标准为18px
            fontWeight: '600', // 增加字重
            color: '#1e293b',
            background: 'rgba(30, 41, 59, 0.08)', // 添加背景底纹，增强视觉识别度
            border: '1px solid rgba(30, 41, 59, 0.15)', // 添加边框
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            minHeight: '44px', // 设置最小高度，提升可点击性
          }}
          onMouseEnter={e => {
            e.currentTarget.style.background = 'rgba(30, 41, 59, 0.15)'; // 悬停时加深背景
            e.currentTarget.style.borderColor = 'rgba(30, 41, 59, 0.25)';
          }}
          onMouseLeave={e => {
            e.currentTarget.style.background = 'rgba(30, 41, 59, 0.08)'; // 恢复原始背景底纹
            e.currentTarget.style.borderColor = 'rgba(30, 41, 59, 0.15)';
          }}
        >
          <Home size={20} /> {/* 增大图标尺寸 */}
          返回首页
        </button>
      </div>

      {/* 中间标题和AI徽章 */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        <h1
          style={{
            fontSize: layoutMode === 'care' ? '28px' : '22px', // 关爱模式28px，普通模式22px
            fontWeight: '600',
            color: '#1e293b',
            margin: 0,
            letterSpacing: '0.5px',
          }}
        >
          重庆银行 AI 问询助手
        </h1>
        <Badge
          style={{
            background: 'linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%)',
            color: 'white',
            fontSize: '11px',
            fontWeight: '600',
            padding: '4px 8px',
            borderRadius: '12px',
            boxShadow: '0 2px 8px rgba(0, 122, 255, 0.3)',
          }}
        >
          AI
        </Badge>
      </div>

      {/* 右侧功能切换按钮 */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
        {' '}
        {/* 增加按钮间距 */}
        {/* 收音模式切换 */}
        <button
          onClick={() => handleToggle('audioMode')}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px', // 增加间距
            padding: '10px 14px', // 增大内边距
            borderRadius: '10px', // 增大圆角
            fontSize: '18px', // 统一普通文字标准为18px
            fontWeight: '600',
            color: toggleState.audioMode === 'button' ? '#007AFF' : '#FF3B30',
            background:
              toggleState.audioMode === 'button'
                ? 'rgba(0, 122, 255, 0.1)'
                : 'rgba(255, 59, 48, 0.1)',
            border: `1px solid ${toggleState.audioMode === 'button' ? '#007AFF' : '#FF3B30'}40`,
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            minHeight: '40px', // 设置最小高度
          }}
          onMouseEnter={e => {
            e.currentTarget.style.transform = 'translateY(-1px)';
          }}
          onMouseLeave={e => {
            e.currentTarget.style.transform = 'translateY(0)';
          }}
        >
          {toggleState.audioMode === 'button' ? <Mic size={16} /> : <MicOff size={16} />}{' '}
          {/* 增大图标 */}
          <span>{toggleState.audioMode === 'button' ? '按键收音' : '唇动收音'}</span>
        </button>
        {/* 语言切换 */}
        <button
          onClick={() => handleToggle('language')}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px', // 增加间距
            padding: '10px 14px', // 增大内边距
            borderRadius: '10px', // 增大圆角
            fontSize: '18px', // 统一普通文字标准为18px
            fontWeight: '600',
            color: toggleState.language === 'chuanyu' ? '#FF9500' : '#34C759',
            background:
              toggleState.language === 'chuanyu'
                ? 'rgba(255, 149, 0, 0.1)'
                : 'rgba(52, 199, 89, 0.1)',
            border: `1px solid ${toggleState.language === 'chuanyu' ? '#FF9500' : '#34C759'}40`,
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            minHeight: '40px', // 设置最小高度
          }}
          onMouseEnter={e => {
            e.currentTarget.style.transform = 'translateY(-1px)';
          }}
          onMouseLeave={e => {
            e.currentTarget.style.transform = 'translateY(0)';
          }}
        >
          <Volume2 size={16} /> {/* 增大图标 */}
          <span>{toggleState.language === 'chuanyu' ? '川渝话' : '普通话'}</span>
        </button>
        {/* 模式切换 */}
        <button
          onClick={() => handleToggle('mode')}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px', // 增加间距
            padding: '10px 14px', // 增大内边距
            borderRadius: '10px', // 增大圆角
            fontSize: '18px', // 统一普通文字标准为18px
            fontWeight: '600',
            color: toggleState.mode === 'standard' ? '#AF52DE' : '#8E8E93',
            background:
              toggleState.mode === 'standard'
                ? 'rgba(175, 82, 222, 0.1)'
                : 'rgba(142, 142, 147, 0.1)',
            border: `1px solid ${toggleState.mode === 'standard' ? '#AF52DE' : '#8E8E93'}40`,
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            minHeight: '40px', // 设置最小高度
          }}
          onMouseEnter={e => {
            e.currentTarget.style.transform = 'translateY(-1px)';
          }}
          onMouseLeave={e => {
            e.currentTarget.style.transform = 'translateY(0)';
          }}
        >
          {toggleState.mode === 'standard' ? <Settings size={16} /> : <Users size={16} />}{' '}
          {/* 增大图标 */}
          <span>{toggleState.mode === 'standard' ? '标准模式' : '关爱模式'}</span>
        </button>
      </div>
    </div>
  );
};
