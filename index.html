<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSDK 开发环境</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 600px;
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
        }

        .description {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .links {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .link-btn {
            display: inline-block;
            padding: 15px 30px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: background 0.3s;
        }

        .link-btn:hover {
            background: #5a67d8;
        }

        .link-btn.secondary {
            background: #48bb78;
        }

        .link-btn.secondary:hover {
            background: #38a169;
        }

        .env-info {
            margin-top: 30px;
            padding: 20px;
            background: #f7fafc;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .env-info h3 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .env-info p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🚀 WebSDK 开发环境</h1>
        <div class="description">
            欢迎使用WebSDK开发环境！这里提供了HTTPS支持，可以模拟真实的生产环境。
        </div>

        <div class="links">
            <a href="/examples/greeting-demo.html" class="link-btn">
                🏠 打招呼页面演示
            </a>
            <a href="/examples/chat-demo.html" class="link-btn secondary">
                💬 聊天组件演示
            </a>
            <a href="/examples/page-manager-test.html" class="link-btn">
                📄 页面管理器测试
                <a href="/examples/simple-demo.html" class="link-btn secondary">
                    🎯 简单演示页面
                </a>
        </div>

        <div class="env-info">
            <h3>🔧 环境信息</h3>
            <p><strong>协议:</strong> HTTPS (自签名证书)</p>
            <p><strong>主机:</strong> 0.0.0.0 (支持IP访问)</p>
            <p><strong>端口:</strong> 3000</p>
            <p><strong>访问方式:</strong> https://localhost:3000 或 https://[你的IP]:3000</p>
        </div>
    </div>

    <script>
        // 显示当前访问地址
        document.addEventListener('DOMContentLoaded', () => {
            const envInfo = document.querySelector('.env-info');
            const currentUrl = window.location.origin;
            envInfo.innerHTML += `<p><strong>当前地址:</strong> ${currentUrl}</p>`;
        });
    </script>
</body>

</html>