#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 构建自包含SDK...');

const distDir = path.resolve(__dirname, '../dist');
const jsFile = path.join(distDir, 'web-service-sdk.js');
const cssFile = path.join(distDir, 'web-service-api-sdk.css');

// 检查并保护静态资源目录
const staticDir = path.join(distDir, 'static');
const humanDir = path.join(staticDir, 'human');

console.log('🛡️  检查静态资源目录...');
if (fs.existsSync(staticDir)) {
  console.log('✅ static 目录存在');
  if (fs.existsSync(humanDir)) {
    console.log('✅ human 目录存在');
    const humanFiles = fs.readdirSync(humanDir, { recursive: true });
    console.log(`📁 发现 ${humanFiles.length} 个静态资源文件`);
  } else {
    console.log('⚠️  human 目录不存在');
  }
} else {
  console.log('⚠️  static 目录不存在');
}

// 检查文件是否存在
if (!fs.existsSync(jsFile)) {
  console.error('❌ JS文件不存在:', jsFile);
  process.exit(1);
}

if (!fs.existsSync(cssFile)) {
  console.error('❌ CSS文件不存在:', cssFile);
  process.exit(1);
}

// 读取文件内容
const jsContent = fs.readFileSync(jsFile, 'utf8');
const cssContent = fs.readFileSync(cssFile, 'utf8');

console.log('📄 JS文件大小:', (jsContent.length / 1024).toFixed(2), 'KB');
console.log('🎨 CSS文件大小:', (cssContent.length / 1024).toFixed(2), 'KB');

// 清理CSS内容，转义特殊字符
const cleanCssContent = cssContent
  .replace(/\\/g, '\\\\')
  .replace(/`/g, '\\`')
  .replace(/\$/g, '\\$')
  .replace(/\r?\n/g, '\\n')
  .trim();

// 创建CSS注入代码
const cssInjectionCode = `
// 自动注入CSS样式到页面
(function() {
  if (typeof document !== 'undefined' && !document.getElementById('web-sdk-styles')) {
    const style = document.createElement('style');
    style.id = 'web-sdk-styles';
    style.textContent = \`${cleanCssContent}\`;
    document.head.appendChild(style);
    console.log('✅ WebSDK样式已注入');
  }
})();

`;

// 将CSS注入代码添加到JS文件开头
const combinedContent = cssInjectionCode + jsContent;

// 写入新的JS文件
fs.writeFileSync(jsFile, combinedContent, 'utf8');

// 删除CSS文件
fs.unlinkSync(cssFile);

console.log('✅ CSS已成功内嵌到JS文件中');
console.log('🗑️  独立的CSS文件已删除');
console.log('📦 最终文件大小:', (combinedContent.length / 1024).toFixed(2), 'KB');
console.log('🎉 自包含SDK构建完成!');
