/**
 * React版本ChatWidget管理器
 * 负责React组件的生命周期管理，参考GreetingPageManager的实现
 */

import * as React from 'react';
import * as ReactDOM from 'react-dom/client';

import { WebSDK } from '../core/WebSDK';
import { Logger } from '../utils/Logger';

import { ChatWidgetReact } from './ChatWidgetReact';

/**
 * ChatWidget管理器
 */
export class ChatWidgetManager {
  private sdk: WebSDK;
  private logger: Logger;
  private container: HTMLElement | null = null;
  private reactRoot: ReactDOM.Root | null = null;
  private isVisible: boolean = false;

  // 组件属性
  private theme: 'light' | 'dark' = 'light';
  private maxMessages: number = 100;
  private enableVoiceInput: boolean = true;
  private avatarUrl: string = '';
  private defaultVoice: string = 'mandarin';

  constructor(sdk: WebSDK, container: HTMLElement) {
    this.sdk = sdk;
    this.container = container;
    this.logger = Logger.getInstance({ prefix: 'ChatWidgetManager' });
  }

  /**
   * 设置组件属性
   */
  public setProps(props: {
    theme?: 'light' | 'dark';
    maxMessages?: number;
    enableVoiceInput?: boolean;
    avatarUrl?: string;
    defaultVoice?: string;
  }): void {
    if (props.theme !== undefined) this.theme = props.theme;
    if (props.maxMessages !== undefined) this.maxMessages = props.maxMessages;
    if (props.enableVoiceInput !== undefined) this.enableVoiceInput = props.enableVoiceInput;
    if (props.avatarUrl !== undefined) this.avatarUrl = props.avatarUrl;
    if (props.defaultVoice !== undefined) this.defaultVoice = props.defaultVoice;

    // 如果组件已经渲染，重新渲染以应用新属性
    if (this.isVisible && this.reactRoot) {
      this.render();
    }
  }

  /**
   * 显示ChatWidget
   */
  public show(): void {
    if (this.isVisible) {
      this.logger.warn('ChatWidget已经显示，跳过重复显示');
      return;
    }

    this.logger.info('显示React版本ChatWidget');

    try {
      if (!this.container) {
        throw new Error('容器元素不存在');
      }

      // 创建React根节点
      this.reactRoot = ReactDOM.createRoot(this.container);

      // 渲染React组件
      this.render();

      this.isVisible = true;
      this.logger.info('React版本ChatWidget已显示');
    } catch (error) {
      this.logger.error('显示ChatWidget失败', error);
      this.cleanup();
    }
  }

  /**
   * 隐藏ChatWidget
   */
  public hide(): void {
    if (!this.isVisible) {
      this.logger.warn('ChatWidget未显示，跳过隐藏操作');
      return;
    }

    this.logger.info('隐藏React版本ChatWidget');
    this.cleanup();
  }

  /**
   * 渲染React组件
   */
  private render(): void {
    if (!this.reactRoot) {
      this.logger.error('React根节点不存在，无法渲染');
      return;
    }

    this.reactRoot.render(
      React.createElement(ChatWidgetReact, {
        sdk: this.sdk,
        theme: this.theme,
        maxMessages: this.maxMessages,
        enableVoiceInput: this.enableVoiceInput,
        avatarUrl: this.avatarUrl,
        defaultVoice: this.defaultVoice,
      })
    );
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    try {
      if (this.reactRoot) {
        this.reactRoot.unmount();
        this.reactRoot = null;
      }

      this.isVisible = false;
      this.logger.info('ChatWidget资源已清理');
    } catch (error) {
      this.logger.error('清理ChatWidget资源失败', error);
    }
  }

  /**
   * 获取显示状态
   */
  public isShowing(): boolean {
    return this.isVisible;
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.hide();
    this.container = null;
  }
}
