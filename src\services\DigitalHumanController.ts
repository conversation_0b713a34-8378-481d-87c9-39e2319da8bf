/**
 * 数字人控制器
 * 负责管理数字人视频的状态，与AI响应状态同步
 */

import { EventBus } from '../core/EventBus';
import { Logger } from '../utils/Logger';

/**
 * 数字人状态枚举
 */
export enum DigitalHumanState {
  IDLE = 'idle',
  SPEAKING = 'speaking',
}

/**
 * 数字人控制器配置
 */
export interface DigitalHumanControllerConfig {
  /** 事件总线 */
  eventBus: EventBus;
  /** 调试模式 */
  debug?: boolean;
}

/**
 * 数字人控制器
 */
export class DigitalHumanController {
  private eventBus: EventBus;
  private logger: Logger;
  private videoElements: HTMLElement[] = [];
  private currentState: DigitalHumanState = DigitalHumanState.IDLE;

  private retryCount: number = 0;

  constructor(config: DigitalHumanControllerConfig) {
    this.eventBus = config.eventBus;
    this.logger = Logger.getInstance({
      prefix: 'DigitalHumanController',
    });

    this.bindEvents();
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // 注意：移除了过时的AI响应监听器，现在只监听TTS事件
    // 数字人状态应该只根据TTS播放状态切换，而不是AI响应状态

    // 监听数字人页面显示事件
    this.eventBus.on('page:show-digital-human', () => {
      this.logger.info('数字人页面显示');
      // 不需要查找元素，直接通过事件通信
    });

    // 监听ChatWidget组件就绪事件
    this.eventBus.on('component:custom-component-ready', () => {
      this.logger.info('ChatWidget组件就绪');
      // 不需要查找元素，直接通过事件通信
    });

    // 监听TTS播放开始事件
    this.eventBus.on('tts:play-start', () => {
      this.logger.info('TTS播放开始，切换到说话状态');
      this.setState(DigitalHumanState.SPEAKING);
    });

    // 监听TTS播放结束事件
    this.eventBus.on('tts:play-end', () => {
      this.logger.info('TTS播放结束，切换到待机状态');
      this.setState(DigitalHumanState.IDLE);
    });
  }

  /**
   * 初始化视频元素
   */
  private initializeVideoElement(): void {
    this.videoElements = [];

    // 查找所有数字人视频元素（现在只有React版本）
    const allDigitalHumanVideos = document.querySelectorAll('simple-digital-human');

    this.logger.info(`找到 ${allDigitalHumanVideos.length} 个数字人视频元素`);

    allDigitalHumanVideos.forEach((element, index) => {
      this.videoElements.push(element as HTMLElement);
      this.logger.info(`数字人视频元素 ${index + 1}: id=${element.id || '无ID'}`);
    });

    // 特别查找已知ID的元素
    const knownIds = [
      'digital-human-video-player',
      'chatwidget-digital-human',
      'chatwidget-digital-human-video',
    ];
    knownIds.forEach(id => {
      const element = document.querySelector(`#${id}`);
      if (element && !this.videoElements.includes(element as HTMLElement)) {
        this.videoElements.push(element as HTMLElement);
        this.logger.info(`通过ID找到额外的视频元素: ${id}`);
      }
    });

    if (this.videoElements.length === 0) {
      this.logger.warn('未找到任何数字人视频元素');
      this.logger.info('尝试重新查找...');
      // 延迟重试，但限制重试次数
      if (!this.retryCount) this.retryCount = 0;
      if (this.retryCount < 5) {
        this.retryCount++;
        setTimeout(() => this.initializeVideoElement(), 1000);
      } else {
        this.logger.error('重试次数已达上限，停止查找视频元素');
      }
    } else {
      this.logger.info(`成功找到 ${this.videoElements.length} 个数字人视频元素`);
      this.retryCount = 0; // 重置重试计数
    }
  }

  // 移除了过时的AI响应处理方法
  // 现在数字人状态只根据TTS播放状态切换

  /**
   * 设置数字人状态
   */
  private setState(state: DigitalHumanState): void {
    // 如果状态没有变化，不做任何操作
    if (this.currentState === state) {
      return;
    }

    this.logger.info('切换数字人状态', {
      from: this.currentState,
      to: state,
    });

    this.currentState = state;

    // 实时查找并更新所有数字人视频元素
    this.updateAllVideoElements(state);
  }

  /**
   * 更新所有视频元素状态
   */
  private updateAllVideoElements(state: DigitalHumanState): void {
    // 通过DOM事件广播状态变化
    const event = new CustomEvent('digital-human-state-change', {
      detail: { state: state, timestamp: Date.now() },
      bubbles: true,
    });
    document.dispatchEvent(event);

    // 同时尝试直接更新DOM元素（作为备用方案）
    const allDigitalHumanVideos = document.querySelectorAll('simple-digital-human');

    if (allDigitalHumanVideos.length > 0) {
      allDigitalHumanVideos.forEach((element, index) => {
        // 对于新的简化组件，调用其方法
        if (element.tagName.toLowerCase() === 'simple-digital-human') {
          const simpleElement = element as HTMLElement & {
            startSpeaking?: () => void;
            stopSpeaking?: () => void;
          };
          if (state === 'speaking' && simpleElement.startSpeaking) {
            simpleElement.startSpeaking();
          } else if (state === 'idle' && simpleElement.stopSpeaking) {
            simpleElement.stopSpeaking();
          }
        } else {
          // 对于旧组件，设置属性
          element.setAttribute('state', state);
        }
        this.logger.debug(`更新视频元素 ${index + 1} 状态为: ${state}`);
      });
      this.logger.info(`成功更新 ${allDigitalHumanVideos.length} 个视频元素状态`);
    } else {
      this.logger.info('通过DOM事件广播状态变化');
    }
  }

  /**
   * 手动切换到说话状态
   */
  public startSpeaking(): void {
    this.setState(DigitalHumanState.SPEAKING);
  }

  /**
   * 手动切换到待机状态
   */
  public stopSpeaking(): void {
    this.setState(DigitalHumanState.IDLE);
  }

  /**
   * 获取当前状态
   */
  public getState(): DigitalHumanState {
    return this.currentState;
  }
}
