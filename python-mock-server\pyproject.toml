[project]
name = "websdk-mock-server"
version = "1.0.0"
description = "Python GUI Mock Server for WebSDK Testing"
authors = [
    {name = "WebSDK Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "websockets>=12.0",
    "aiofiles>=23.2.1",
    "pydantic>=2.5.0",
    "python-multipart>=0.0.6",
    "requests>=2.31.0",
    "aiohttp>=3.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
line_length = 88

[tool.hatch.build.targets.wheel]
packages = ["."]

[project.scripts]
mock-server = "start:main"
