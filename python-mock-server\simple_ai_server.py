#!/usr/bin/env python3
"""
简化的AI服务器模拟器
严格遵循docs/json-rpc.md规范的JSON-RPC 2.0模拟服务器
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, Optional, Any

import uvicorn
from fastapi import FastAPI, Request
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel


class JsonRpcRequest(BaseModel):
    """JSON-RPC 2.0 请求格式"""
    jsonrpc: str = "2.0"
    method: str
    params: Dict[str, Any] = {}
    id: str
    context: Optional[Dict[str, Any]] = None


class JsonRpcResponse(BaseModel):
    """JSON-RPC 2.0 成功响应格式"""
    jsonrpc: str = "2.0"
    result: Dict[str, Any]
    id: str

    class Config:
        # 只序列化非None字段
        exclude_none = True


class JsonRpcErrorResponse(BaseModel):
    """JSON-RPC 2.0 错误响应格式"""
    jsonrpc: str = "2.0"
    error: Dict[str, Any]
    id: str

    class Config:
        exclude_none = True


class SimpleAIServer:
    """简化的AI服务器模拟器"""

    def __init__(self, port: int = 8080):
        self.port = port
        self.app = FastAPI(title="简化AI服务器模拟器", version="1.0.0")
        self.setup_cors()
        self.setup_routes()

    def setup_cors(self):
        """设置CORS"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

    def setup_routes(self):
        """设置路由"""
        @self.app.post("/rpc")
        async def handle_jsonrpc(request: Request):
            return await self.handle_jsonrpc_request(request)

    async def handle_jsonrpc_request(self, request: Request):
        """处理JSON-RPC请求"""
        try:
            print(f"\n🌐 收到HTTP请求: {request.method} {request.url}")
            print(f"   Headers: {dict(request.headers)}")

            body = await request.body()
            print(f"   Body: {body.decode('utf-8')}")

            request_data = json.loads(body.decode('utf-8'))

            # 验证JSON-RPC格式
            if request_data.get("jsonrpc") != "2.0":
                return JsonRpcErrorResponse(
                    id=request_data.get("id", "unknown"),
                    error={
                        "code": -32600,
                        "message": "Invalid Request: jsonrpc字段必须为'2.0'"
                    }
                ).model_dump()

            rpc_request = JsonRpcRequest(**request_data)

            print(f"\n📥 解析JSON-RPC请求:")
            print(f"   方法: {rpc_request.method}")
            print(f"   ID: {rpc_request.id}")
            print(f"   参数: {json.dumps(rpc_request.params, ensure_ascii=False, indent=2)}")
            if rpc_request.context:
                print(f"   上下文: {json.dumps(rpc_request.context, ensure_ascii=False, indent=2)}")

            # 根据方法分发处理
            if rpc_request.method == "speak":
                return await self.handle_speak(rpc_request)
            elif rpc_request.method == "chat":
                return await self.handle_chat_stream(rpc_request)
            elif rpc_request.method == "updateBackgroundInfo":
                return await self.handle_update_background_info(rpc_request)
            elif rpc_request.method == "addMessages":
                return await self.handle_add_messages(rpc_request)
            elif rpc_request.method == "pushBizData":
                return await self.handle_push_biz_data(rpc_request)
            else:
                return JsonRpcErrorResponse(
                    id=rpc_request.id,
                    error={
                        "code": -32601,
                        "message": f"Method not found: {rpc_request.method}"
                    }
                ).model_dump()

        except json.JSONDecodeError:
            return JsonRpcErrorResponse(
                id="unknown",
                error={
                    "code": -32700,
                    "message": "Parse error: 无效的JSON格式"
                }
            ).model_dump()
        except Exception as e:
            print(f"❌ 处理请求失败: {e}")
            return JsonRpcErrorResponse(
                id=request_data.get("id", "unknown") if 'request_data' in locals() else "unknown",
                error={
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                }
            ).model_dump()

    async def handle_speak(self, request: JsonRpcRequest):
        """处理speak请求 - 根据文档，speak可能是单向通知"""
        text = request.params.get("text", "")
        delay = request.params.get("delay", 0)
        display = request.params.get("display", True)

        if not text:
            return JsonRpcErrorResponse(
                id=request.id,
                error={
                    "code": -32602,
                    "message": "Invalid params: 缺少必需参数 text"
                }
            ).model_dump()

        print(f"🗣️ 处理speak请求:")
        print(f"   文本: {text}")
        print(f"   延时: {delay}ms")
        print(f"   显示: {display}")
        if request.context:
            print(f"   上下文: {json.dumps(request.context, ensure_ascii=False, indent=2)}")

        # 根据文档，speak方法通常不需要响应（单向通知）
        # 但如果有id字段，说明客户端期望响应
        if request.id:
            return JsonRpcResponse(
                id=request.id,
                result={}  # 空结果表示操作成功
            ).model_dump()
        else:
            # 无id的通知，不返回响应
            return None

    async def handle_chat_stream(self, request: JsonRpcRequest):
        """处理chat请求，返回SSE流式响应"""
        # 支持两种参数格式：userInput（文档标准）和message（测试兼容）
        user_input = request.params.get("userInput") or request.params.get("message", "")
        session_id = request.params.get("sessionId", "")

        if not user_input:
            return JsonRpcErrorResponse(
                id=request.id,
                error={
                    "code": -32602,
                    "message": "Invalid params: 缺少必需参数 userInput 或 message"
                }
            ).model_dump()

        # 如果没有sessionId，创建一个新的
        if not session_id:
            session_id = str(uuid.uuid4())
            print(f"🆕 创建新会话ID: {session_id}")

        print(f"💬 处理chat请求:")
        print(f"   用户输入: {user_input}")
        print(f"   会话ID: {session_id}")

        # 返回SSE流式响应
        return StreamingResponse(
            self.generate_chat_stream(request.id, user_input, session_id),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )

    async def generate_chat_stream(self, request_id: str, user_input: str, session_id: str):
        """生成聊天流式响应"""
        print(f"🌊 开始SSE流式响应: {request_id}")

        try:
            # 模拟AI回复
            mock_response = f"你好！我收到了你的消息：「{user_input}」。这是一个模拟的AI回复。"
            
            # 分段发送流式响应
            words = mock_response.split()
            accumulated_message = ""
            
            for i, word in enumerate(words):
                accumulated_message += word
                
                # 发送聊天流式响应通知
                stream_notification = {
                    "jsonrpc": "2.0",
                    "method": "notifications/chatStreamResponse",
                    "params": {
                        "message": word,
                        "requestId": request_id,
                        "sessionId": session_id
                    }
                }
                
                yield f"data: {json.dumps(stream_notification, ensure_ascii=False)}\n\n"
                
                # 模拟延时
                await asyncio.sleep(0.1)
                
                if i < len(words) - 1:
                    accumulated_message += " "

            # 发送最终响应
            final_response = {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "message": accumulated_message,
                    "sessionId": session_id
                }
            }
            
            yield f"data: {json.dumps(final_response, ensure_ascii=False)}\n\n"
            print(f"✅ SSE流式响应完成: {request_id}")

        except Exception as e:
            print(f"❌ SSE流式响应错误: {e}")
            error_response = {
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                }
            }
            yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"

    async def handle_update_background_info(self, request: JsonRpcRequest):
        """处理updateBackgroundInfo请求"""
        session_id = request.params.get("sessionId", "")
        page = request.params.get("page", "")
        status = request.params.get("status", "")

        # 如果没有sessionId，创建一个新的
        if not session_id:
            session_id = str(uuid.uuid4())
            print(f"🆕 创建新会话ID: {session_id}")

        print(f"📝 更新背景信息:")
        print(f"   会话ID: {session_id}")
        print(f"   页面: {page}")
        print(f"   状态: {status}")
        if request.context:
            print(f"   上下文: {json.dumps(request.context, ensure_ascii=False, indent=2)}")

        # 根据文档，返回格式应该只包含sessionId
        return JsonRpcResponse(
            id=request.id,
            result={
                "sessionId": session_id
            }
        ).model_dump()

    async def handle_add_messages(self, request: JsonRpcRequest):
        """处理addMessages请求"""
        session_id = request.params.get("sessionId", "")
        messages = request.params.get("messages", [])

        # 如果没有sessionId，创建一个新的
        if not session_id:
            session_id = str(uuid.uuid4())
            print(f"🆕 创建新会话ID: {session_id}")

        print(f"💬 添加消息:")
        print(f"   会话ID: {session_id}")
        print(f"   消息数量: {len(messages)}")
        if request.context:
            print(f"   上下文: {json.dumps(request.context, ensure_ascii=False, indent=2)}")

        # 根据文档，addMessages响应格式应该只包含sessionId
        return JsonRpcResponse(
            id=request.id,
            result={
                "sessionId": session_id
            }
        ).model_dump()

    async def handle_push_biz_data(self, request: JsonRpcRequest):
        """处理pushBizData请求"""
        key = request.params.get("key", "")
        data = request.params.get("data")

        # 参数验证
        if not key:
            return JsonRpcErrorResponse(
                id=request.id,
                error={
                    "code": -32602,
                    "message": "Invalid params: 缺少必需参数 key"
                }
            ).model_dump()

        if data is None:
            return JsonRpcErrorResponse(
                id=request.id,
                error={
                    "code": -32602,
                    "message": "Invalid params: 缺少必需参数 data"
                }
            ).model_dump()

        print(f"📊 推送业务数据:")
        print(f"   键: {key}")
        print(f"   数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        if request.context:
            print(f"   上下文: {json.dumps(request.context, ensure_ascii=False, indent=2)}")

        # 根据文档，pushBizData的响应格式应该只包含success字段
        return JsonRpcResponse(
            id=request.id,
            result={
                "success": True
            }
        ).model_dump()

    def run(self):
        """启动服务器"""
        print(f"🚀 启动简化AI服务器模拟器，端口: {self.port}")
        print(f"📖 JSON-RPC端点: http://localhost:{self.port}/rpc")
        uvicorn.run(self.app, host="0.0.0.0", port=self.port)


if __name__ == "__main__":
    server = SimpleAIServer(port=8080)
    server.run()
