/**
 * 页面状态管理器
 * 负责管理WebSDK的多页面状态切换功能
 */

import { Logger } from '../utils/Logger';

import { EventBus } from './EventBus';

/**
 * 页面状态类型定义
 */
export type PageState = 'WaitCardPage' | 'AppSelectAuto' | 'CreditSelectAuto';

/**
 * 页面状态变化事件数据
 */
export interface PageStateChangeEvent {
  previousState: PageState;
  currentState: PageState;
  timestamp: number;
}

/**
 * 页面状态管理器
 */
export class PageStateManager {
  private eventBus: EventBus;
  private logger: Logger;
  private currentState: PageState = 'WaitCardPage'; // 默认状态为全功能页面

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.logger = Logger.getInstance({ prefix: 'PageStateManager' });

    this.logger.info('页面状态管理器已初始化', {
      defaultState: this.currentState,
    });
  }

  /**
   * 获取当前页面状态
   */
  public getCurrentState(): PageState {
    return this.currentState;
  }

  /**
   * 设置页面状态
   */
  public setState(newState: PageState): void {
    if (this.currentState === newState) {
      this.logger.debug('页面状态未变化，跳过切换', {
        currentState: this.currentState,
        requestedState: newState,
      });
      return;
    }

    const previousState = this.currentState;
    this.currentState = newState;

    this.logger.info('页面状态已切换', {
      from: previousState,
      to: newState,
      timestamp: Date.now(),
    });

    // 发送页面状态变化事件
    const eventData: PageStateChangeEvent = {
      previousState,
      currentState: newState,
      timestamp: Date.now(),
    };

    this.eventBus.emit('page-state:changed', eventData);
  }

  /**
   * 检查是否为全功能页面
   */
  public isFullFunctionPage(): boolean {
    return this.currentState === 'WaitCardPage';
  }

  /**
   * 检查是否为储蓄卡页面
   */
  public isSavingsCardPage(): boolean {
    return this.currentState === 'AppSelectAuto';
  }

  /**
   * 检查是否为信用卡页面
   */
  public isCreditCardPage(): boolean {
    return this.currentState === 'CreditSelectAuto';
  }

  /**
   * 获取页面状态的中文描述
   */
  public getStateDescription(state?: PageState): string {
    const targetState = state || this.currentState;

    switch (targetState) {
      case 'WaitCardPage':
        return '全功能页面';
      case 'AppSelectAuto':
        return '储蓄卡业务页面';
      case 'CreditSelectAuto':
        return '信用卡业务页面';
      default:
        return '未知页面状态';
    }
  }

  /**
   * 重置到默认状态
   */
  public reset(): void {
    this.setState('WaitCardPage');
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.logger.info('页面状态管理器已销毁');
  }
}
