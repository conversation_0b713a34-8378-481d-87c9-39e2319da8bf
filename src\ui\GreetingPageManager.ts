/**
 * React版本打招呼页面管理器
 * 负责React组件的生命周期管理和页面级显示/隐藏
 */

import * as React from 'react';
import * as ReactDOM from 'react-dom/client';

import { WebSDK } from '../core/WebSDK';
import { Logger } from '../utils/Logger';

import { GreetingPageReact } from './GreetingPageReact';

/**
 * 打招呼页面管理器
 */
export class GreetingPageManager {
  private sdk: WebSDK;
  private logger: Logger;
  private container: HTMLElement | null = null;
  private reactRoot: ReactDOM.Root | null = null;
  private isVisible: boolean = false;

  constructor(sdk: WebSDK) {
    this.sdk = sdk;
    this.logger = Logger.getInstance({ prefix: 'GreetingPageManager' });
  }

  /**
   * 显示打招呼页面
   */
  public show(): void {
    if (this.isVisible) {
      this.logger.warn('打招呼页面已经显示，跳过重复显示');
      return;
    }

    this.logger.info('显示React版本打招呼页面');

    try {
      // 创建容器元素
      this.container = document.createElement('div');
      this.container.id = 'greeting-page-react-root';
      this.container.style.position = 'fixed';
      this.container.style.top = '0';
      this.container.style.left = '0';
      this.container.style.width = '100%';
      this.container.style.height = '100%';
      this.container.style.zIndex = '9999';

      // 添加到DOM
      document.body.appendChild(this.container);

      // 创建React根节点
      this.reactRoot = ReactDOM.createRoot(this.container);

      // 渲染React组件
      this.reactRoot.render(
        React.createElement(GreetingPageReact, {
          sdk: this.sdk,
          title: '欢迎使用数字人服务',
          showCloseButton: true,
          onClose: () => this.hide(),
        })
      );

      this.isVisible = true;
      this.logger.info('React版本打招呼页面已显示');
    } catch (error) {
      this.logger.error('显示打招呼页面失败', error);
      this.cleanup();
    }
  }

  /**
   * 隐藏打招呼页面
   */
  public hide(): void {
    if (!this.isVisible) {
      this.logger.warn('打招呼页面已经隐藏，跳过重复隐藏');
      return;
    }

    this.logger.info('隐藏React版本打招呼页面');
    this.cleanup();
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    try {
      // 卸载React组件
      if (this.reactRoot) {
        this.reactRoot.unmount();
        this.reactRoot = null;
      }

      // 移除DOM容器
      if (this.container && this.container.parentNode) {
        this.container.parentNode.removeChild(this.container);
        this.container = null;
      }

      this.isVisible = false;
      this.logger.info('React版本打招呼页面资源已清理');
    } catch (error) {
      this.logger.error('清理打招呼页面资源失败', error);
    }
  }

  /**
   * 检查是否可见
   */
  public isGreetingPageVisible(): boolean {
    return this.isVisible;
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.hide();
    this.logger.info('打招呼页面管理器已销毁');
  }
}
