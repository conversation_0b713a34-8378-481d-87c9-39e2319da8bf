{"name": "web-service-api-sdk", "version": "1.0.0", "description": "Web服务API包装器SDK - 支持双WebSocket连接和HTTP通信的统一接口", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "pnpm run prebuild && vite build && node scripts/build-self-contained.js", "build:lib": "pnpm run prebuild && vite build", "build:rollup": "pnpm run prebuild && rollup -c", "build:watch": "vite build --watch", "prebuild": "pnpm run type-check && pnpm run lint:fix", "dev": "node scripts/dev-server.js", "dev:http": "node scripts/dev-server.js --http", "dev:https": "node scripts/dev-server.js --https", "demo": "node start-demo.js", "dev:demo": "vite demo", "preview": "vite preview", "lint": "eslint src/**/*.{ts,tsx}", "lint:fix": "eslint src/**/*.{ts,tsx} --fix", "format": "prettier --write src/**/*.{ts,tsx}", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "test": "vitest", "test:run": "vitest run", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "prepublishOnly": "pnpm run clean && pnpm run build"}, "keywords": ["websocket", "http", "api", "sdk", "typescript", "javascript"], "author": "WebSDK Team", "license": "MIT", "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.6", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20.11.19", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "@vitejs/plugin-basic-ssl": "^2.1.0", "@vitejs/plugin-react": "^4.6.0", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "jsdom": "^26.1.0", "msw": "^2.10.4", "postcss": "^8.5.6", "prettier": "^3.2.5", "rimraf": "^5.0.5", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "tslib": "^2.6.2", "typescript": "^5.3.3", "vite": "^7.0.4", "vite-plugin-checker": "^0.10.0", "vitest": "^3.2.4"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "git+http://git.withufuture.com/ai-llm/ai_web_sdk.git"}, "bugs": {"url": "http://git.withufuture.com/ai-llm/ai_web_sdk/-/issues"}, "homepage": "http://git.withufuture.com/ai-llm/ai_web_sdk#readme", "dependencies": {"@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lit": "^3.3.1", "lucide-react": "^0.525.0", "nanoid": "^5.1.5", "ofetch": "^1.4.1", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1"}}