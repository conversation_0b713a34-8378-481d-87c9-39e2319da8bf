#!/usr/bin/env python3
"""
HKSTT服务器测试脚本
用于验证HKSTT服务器的基础功能，专注于MVP核心功能
"""

import asyncio
import json
import websockets
from datetime import datetime
import uuid


class HKSTTServerTest:
    """HKSTT服务器测试类""" 
    
    def __init__(self, port: int = 8001):
        self.port = port
        self.server = None
        self.clients = set()
        self.running = False
    
    async def start_server(self):
        """启动简化的HKSTT服务器"""
        print(f"启动HKSTT服务器，端口: {self.port}")
        
        self.server = await websockets.serve(
            self.handle_client,
            "0.0.0.0",  # 监听所有网络接口，支持IP访问
            self.port,
            ping_interval=20,
            ping_timeout=10
        )

        self.running = True
        print(f"HKSTT服务器启动成功: ws://0.0.0.0:{self.port}")
        print("可通过以下地址访问:")
        print(f"  - ws://localhost:{self.port}")
        print(f"  - ws://127.0.0.1:{self.port}")
        print(f"  - ws://*************:{self.port} (请根据实际IP调整)")
        
        # 保持服务器运行
        await self.server.wait_closed()
    
    async def handle_client(self, websocket, path=None):
        """处理SDK客户端连接"""
        client_addr = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        print(f"SDK客户端连接: {client_addr}")
        
        self.clients.add(websocket)
        
        try:
            # 发送HKSTT服务就绪通知
            welcome_message = {
                "jsonrpc": "2.0",
                "method": "notifications/serviceReady",
                "params": {
                    "service": "HKSTT",
                    "version": "1.0.0",
                    "capabilities": ["faceDetection", "asrOffline"],
                    "timestamp": datetime.now().isoformat()
                }
            }
            await websocket.send(json.dumps(welcome_message, ensure_ascii=False))
            print(f"已发送服务就绪通知给: {client_addr}")
            
            # 监听SDK的JSON-RPC消息
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.handle_jsonrpc_message(websocket, data)
                except json.JSONDecodeError:
                    print(f"收到无效JSON: {message}")
                except Exception as e:
                    print(f"处理消息错误: {e}")
        
        except websockets.exceptions.ConnectionClosed:
            print(f"SDK客户端断开连接: {client_addr}")
        except Exception as e:
            print(f"客户端连接错误: {e}")
        finally:
            self.clients.discard(websocket)
    
    async def handle_jsonrpc_message(self, websocket, data):
        """处理SDK发送的JSON-RPC消息"""
        client_addr = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"

        print(f"\n📨 收到来自 {client_addr} 的消息:")
        print(f"   原始数据: {json.dumps(data, ensure_ascii=False, indent=2)}")

        if data.get("jsonrpc") != "2.0":
            print(f"❌ 非JSON-RPC消息: {data}")
            return

        method = data.get("method")
        params = data.get("params", {})
        request_id = data.get("id")

        print(f"   方法: {method}")
        print(f"   参数: {json.dumps(params, ensure_ascii=False, indent=2) if params else 'None'}")
        print(f"   请求ID: {request_id}")

        if method == "ping":
            # 响应ping请求
            response = {
                "jsonrpc": "2.0",
                "result": {
                    "status": "ok",
                    "timestamp": datetime.now().isoformat()
                },
                "id": request_id
            }

            print(f"\n📤 发送ping响应给 {client_addr}:")
            print(f"   响应数据: {json.dumps(response, ensure_ascii=False, indent=2)}")

            await websocket.send(json.dumps(response, ensure_ascii=False))
            print("✅ ping响应发送成功")

        elif method == "getStatus":
            # 发送HKSTT服务状态
            response = {
                "jsonrpc": "2.0",
                "result": {
                    "service": "HKSTT",
                    "status": "running",
                    "clients_count": len(self.clients),
                    "capabilities": ["faceDetection", "asrOffline"],
                    "timestamp": datetime.now().isoformat()
                },
                "id": request_id
            }

            print(f"\n📤 发送状态响应给 {client_addr}:")
            print(f"   响应数据: {json.dumps(response, ensure_ascii=False, indent=2)}")

            await websocket.send(json.dumps(response, ensure_ascii=False))
            print("✅ 状态响应发送成功")

        elif method == "startAudio":
            # 处理开始录音请求
            response = {
                "jsonrpc": "2.0",
                "result": {
                    "status": "recording_started",
                    "timestamp": datetime.now().isoformat()
                },
                "id": request_id
            }

            print(f"\n🎤 处理开始录音请求 from {client_addr}:")
            print(f"   响应数据: {json.dumps(response, ensure_ascii=False, indent=2)}")

            await websocket.send(json.dumps(response, ensure_ascii=False))
            print("✅ 开始录音响应发送成功")

        elif method == "endAudio":
            # 处理结束录音请求
            response = {
                "jsonrpc": "2.0",
                "result": {
                    "status": "recording_stopped",
                    "timestamp": datetime.now().isoformat()
                },
                "id": request_id
            }

            print(f"\n🛑 处理结束录音请求 from {client_addr}:")
            print(f"   响应数据: {json.dumps(response, ensure_ascii=False, indent=2)}")

            await websocket.send(json.dumps(response, ensure_ascii=False))
            print("✅ 结束录音响应发送成功")

        elif method == "speakMode":
            # 处理收音模式设置请求 - 根据业务逻辑，所有speakMode请求都不返回响应
            speak_mode = params.get("speakMode")

            if speak_mode not in [0, 1]:
                print(f"   无效的speakMode值: {speak_mode}，应为0（唇动收音）或1（按键收音）")
            else:
                mode_name = "唇动收音" if speak_mode == 0 else "按键收音"
                print(f"✅ 收音模式已设置为: {mode_name} (speakMode={speak_mode})")
                print("📝 业务逻辑：所有speakMode请求都不返回响应")

        else:
            print(f"⚠️ 收到未知方法: {method}")
            print(f"   参数: {json.dumps(params, ensure_ascii=False, indent=2) if params else 'None'}")
    
    async def send_face_status_notification(self, has_face: bool):
        """发送人脸检测状态通知"""
        if not self.clients:
            print("⚠️ 没有连接的SDK客户端，无法发送人脸状态通知")
            return

        # 根据文档，HKSTT发送的人脸状态事件格式应该是：
        # {"hasFace": True} 或 {"hasFace": False}
        # 但是为了与JSON-RPC协议兼容，我们包装成notification格式
        notification = {
            "jsonrpc": "2.0",
            "method": "notifications/faceStatus",
            "params": {
                "hasFace": has_face,
                "timestamp": datetime.now().isoformat()
            }
        }

        status_text = "检测到人脸" if has_face else "未检测到人脸"
        print(f"\n📡 广播人脸状态通知:")
        print(f"   状态: {status_text}")
        print(f"   目标客户端数量: {len(self.clients)}")
        print(f"   通知数据: {json.dumps(notification, ensure_ascii=False, indent=2)}")

        message_json = json.dumps(notification, ensure_ascii=False)
        disconnected_clients = set()
        successful_sends = 0

        for i, client in enumerate(self.clients, 1):
            try:
                client_addr = f"{client.remote_address[0]}:{client.remote_address[1]}"
                print(f"   📤 发送给客户端 {i}: {client_addr}")

                await client.send(message_json)
                successful_sends += 1
                print(f"   ✅ 发送成功")

            except websockets.exceptions.ConnectionClosed:
                print(f"   ❌ 客户端连接已关闭")
                disconnected_clients.add(client)
            except Exception as e:
                print(f"   ❌ 发送失败: {e}")
                disconnected_clients.add(client)

        # 移除断开连接的客户端
        self.clients -= disconnected_clients

        print(f"📊 广播结果:")
        print(f"   成功发送: {successful_sends} 个客户端")
        print(f"   失败/断开: {len(disconnected_clients)} 个客户端")
        print(f"   当前活跃客户端: {len(self.clients)} 个")
    
    async def send_asr_offline_result(self, text: str, session_id: str | None = None):
        """发送ASR离线识别结果"""
        if not self.clients:
            print("⚠️ 没有连接的SDK客户端，无法发送ASR识别结果")
            return

        sid = session_id or str(uuid.uuid4())
        notification = {
            "jsonrpc": "2.0",
            "method": "notifications/asrOfflineResult",
            "params": {
                "sid": sid,
                "text": text
            }
        }

        print(f"\n🎤 广播ASR识别结果:")
        print(f"   识别文本: {text}")
        print(f"   会话ID: {sid}")
        print(f"   目标客户端数量: {len(self.clients)}")
        print(f"   通知数据: {json.dumps(notification, ensure_ascii=False, indent=2)}")

        message_json = json.dumps(notification, ensure_ascii=False)
        disconnected_clients = set()
        successful_sends = 0

        for i, client in enumerate(self.clients, 1):
            try:
                client_addr = f"{client.remote_address[0]}:{client.remote_address[1]}"
                print(f"   📤 发送给客户端 {i}: {client_addr}")

                await client.send(message_json)
                successful_sends += 1
                print(f"   ✅ 发送成功")

            except websockets.exceptions.ConnectionClosed:
                print(f"   ❌ 客户端连接已关闭")
                disconnected_clients.add(client)
            except Exception as e:
                print(f"   ❌ 发送失败: {e}")
                disconnected_clients.add(client)

        # 移除断开连接的客户端
        self.clients -= disconnected_clients

        print(f"📊 广播结果:")
        print(f"   成功发送: {successful_sends} 个客户端")
        print(f"   失败/断开: {len(disconnected_clients)} 个客户端")
        print(f"   当前活跃客户端: {len(self.clients)} 个")
    
    def stop(self):
        """停止服务器"""
        if self.server:
            self.running = False
            self.server.close()
            print("HKSTT服务器已停止")


async def interactive_test(server):
    """交互式测试功能 - 用户友好的数字选择菜单"""
    import sys
    import threading
    from queue import Queue

    # 创建输入队列
    input_queue = Queue()

    def input_thread():
        """输入线程"""
        while server.running:
            try:
                user_input = input().strip()
                input_queue.put(user_input)
            except (EOFError, KeyboardInterrupt):
                input_queue.put("6")  # 退出
                break

    # 启动输入线程
    input_thread_obj = threading.Thread(target=input_thread, daemon=True)
    input_thread_obj.start()

    def show_menu():
        """显示菜单"""
        print("\n" + "="*50)
        print("🤖 HKSTT服务器交互式测试菜单")
        print("="*50)
        print("1. 发送'新用户检测'事件 (hkstt:newUser)")
        print("2. 发送'用户离开'事件 (hkstt:userLeft)")
        print("3. 发送语音识别结果 (hkstt:userInput)")
        print("4. 模拟按键收音流程 (startAudio → ASR结果)")
        print("5. 显示服务器状态")
        print("6. 退出程序")
        print("-"*50)
        print(f"当前连接的SDK客户端数量: {len(server.clients)}")
        print("-"*50)
        print("请输入选项数字 (1-6): ", end="", flush=True)

    # 显示初始菜单
    show_menu()

    # 预设的ASR测试文本
    asr_test_texts = [
        "你好，我想了解一下服务",
        "请帮我查询账户余额",
        "我要办理业务",
        "谢谢，再见",
        "请问营业时间是什么时候"
    ]
    asr_index = 0

    while server.running:
        try:
            # 检查输入队列
            if not input_queue.empty():
                user_input = input_queue.get()

                if user_input == "1":
                    print("\n✅ 发送'检测到人脸'事件...")
                    if server.clients:
                        await server.send_face_status_notification(True)
                        print("✅ 事件发送成功！")
                    else:
                        print("⚠️  没有连接的SDK客户端")

                elif user_input == "2":
                    print("\n❌ 发送'失去人脸'事件...")
                    if server.clients:
                        await server.send_face_status_notification(False)
                        print("✅ 事件发送成功！")
                    else:
                        print("⚠️  没有连接的SDK客户端")

                elif user_input == "3":
                    print("\n🎤 发送ASR识别结果...")
                    if server.clients:
                        # 使用预设文本，循环使用
                        test_text = asr_test_texts[asr_index % len(asr_test_texts)]
                        asr_index += 1

                        await server.send_asr_offline_result(test_text)
                        print(f"✅ ASR结果发送成功: '{test_text}'")
                    else:
                        print("⚠️  没有连接的SDK客户端")

                elif user_input == "4":
                    print("\n🎯 模拟按键收音流程...")
                    if server.clients:
                        # 模拟按键收音流程：等待用户点击按钮 → 自动发送ASR结果
                        print("   📝 说明：此选项模拟用户点击按键收音按钮后的完整流程")
                        print("   📝 SDK会发送startAudio请求，然后我们自动发送ASR结果")
                        print("   📝 ASR结果会触发SDK自动结束录音状态")
                        print("   ⏳ 等待3秒后自动发送ASR结果...")

                        # 等待3秒模拟录音过程
                        await asyncio.sleep(3)

                        # 发送ASR识别结果（这会触发SDK自动结束录音）
                        test_text = asr_test_texts[asr_index % len(asr_test_texts)]
                        asr_index += 1

                        await server.send_asr_offline_result(test_text)
                        print(f"✅ 按键收音流程完成，ASR结果: '{test_text}'")
                        print("   📝 SDK应该会自动结束录音状态并显示识别结果")
                    else:
                        print("⚠️  没有连接的SDK客户端")

                elif user_input == "5":
                    print("\n📊 服务器状态:")
                    print(f"   - 服务器运行状态: {'✅ 运行中' if server.running else '❌ 已停止'}")
                    print(f"   - 监听端口: {server.port}")
                    print(f"   - 连接的SDK客户端: {len(server.clients)} 个")
                    if server.clients:
                        for i, client in enumerate(server.clients, 1):
                            addr = f"{client.remote_address[0]}:{client.remote_address[1]}"
                            print(f"     {i}. {addr}")

                elif user_input == "6":
                    print("\n👋 正在退出...")
                    server.stop()
                    break

                else:
                    print(f"\n❌ 无效选项: '{user_input}'")
                    print("请输入 1-6 之间的数字")

                # 显示菜单（除了退出）
                if user_input != "6":
                    await asyncio.sleep(1)  # 短暂暂停让用户看到结果
                    show_menu()

            # 短暂休眠避免CPU占用过高
            await asyncio.sleep(0.1)

        except KeyboardInterrupt:
            print("\n👋 收到中断信号，正在退出...")
            server.stop()
            break


async def main():
    """主函数 - 启动HKSTT服务器并提供交互式测试"""
    server = HKSTTServerTest(port=8001)

    print("=== HKSTT服务器MVP测试 ===")
    print("启动服务器...")

    # 在后台启动服务器
    server_task = asyncio.create_task(server.start_server())

    # 等待一下让服务器启动
    await asyncio.sleep(1)

    print("\n服务器已启动，等待SDK连接...")
    print("请在浏览器中打开 demo/mvp-test.html 进行测试")
    print("\n按 Ctrl+C 停止服务器")

    try:
        # 启动交互式测试
        test_task = asyncio.create_task(interactive_test(server))

        # 等待任一任务完成
        done, pending = await asyncio.wait(
            [server_task, test_task],
            return_when=asyncio.FIRST_COMPLETED
        )

        # 取消未完成的任务
        for task in pending:
            task.cancel()

    except KeyboardInterrupt:
        print("\n正在停止服务器...")
        server.stop()
        print("服务器已停止")


if __name__ == "__main__":
    asyncio.run(main())
