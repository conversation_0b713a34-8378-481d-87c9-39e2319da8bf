#!/usr/bin/env python3
"""
AI响应适配器演示和测试脚本
"""

import asyncio
import json
import sys
import os
import time
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from adapters.ai_response_adapter import AIResponseAdapter, create_ai_response


async def test_adapter_directly():
    """直接测试适配器功能"""
    print("🧪 开始直接测试适配器功能")
    print("=" * 50)
    
    adapter = AIResponseAdapter()
    request_id = "test_req_001"
    session_id = "test_session_123"
    
    # 测试1：流式响应
    print("\n📡 测试流式响应适配")
    messages = ["你好，", "我是AI助手，", "很高兴为您服务！"]
    
    for i, msg in enumerate(messages):
        ai_response = create_ai_response(2, msg, session_id)
        adapted = adapter.adapt_response(ai_response, request_id)

        print(f"  流式消息 {i+1}: {msg}")
        if adapted:
            print(f"  适配结果: {adapted['method']} - {adapted['params']['message']}")
        else:
            print(f"  适配结果: None (被过滤)")
    
    # 测试2：最终响应
    print("\n✅ 测试最终响应适配")
    final_ai_response = create_ai_response(0, "完整对话", session_id)
    final_adapted = adapter.adapt_response(final_ai_response, request_id)
    
    print(f"  最终消息: 完整对话")
    if final_adapted:
        print(f"  适配结果: {final_adapted['result']['message']}")
        print(f"  消息合并: {'你好，我是AI助手，很高兴为您服务！'}")
    else:
        print(f"  适配结果: None")
    
    # 测试3：错误响应
    print("\n❌ 测试错误响应适配")
    error_ai_response = {
        "code": 1,
        "data": {"sessionId": session_id, "source": 6, "message": "", "extra": None},
        "errorMsg": "测试错误",
        "language": "zh-CN"
    }
    error_adapted = adapter.adapt_response(error_ai_response, "error_req")
    
    print(f"  错误消息: 测试错误")
    if error_adapted:
        print(f"  适配结果: {error_adapted['error']['message']}")
    else:
        print(f"  适配结果: None")
    
    # 测试4：中间状态过滤
    print("\n🔄 测试中间状态过滤")
    intermediate_ai_response = create_ai_response(1, "中间状态", session_id)
    intermediate_adapted = adapter.adapt_response(intermediate_ai_response, "intermediate_req")
    
    print(f"  中间状态消息: 中间状态")
    print(f"  适配结果: {intermediate_adapted} (应该为None)")
    
    # 状态检查
    print("\n📊 适配器状态")
    status = adapter.get_status()
    for key, value in status.items():
        print(f"  {key}: {value}")
    
    print("\n✅ 直接测试完成")


async def test_server_integration():
    """测试与服务器的集成（简化版）"""
    print("\n🌐 服务器集成测试")
    print("=" * 50)
    print("注意：此功能需要安装aiohttp库")
    print("安装命令: pip install aiohttp")
    print("然后启动服务器: python ai_server.py")
    print("✅ 服务器集成测试跳过")


async def performance_test():
    """性能测试"""
    print("\n⚡ 开始性能测试")
    print("=" * 50)
    
    adapter = AIResponseAdapter()
    
    # 测试大量并发请求
    print("\n🚀 测试并发处理能力")
    start_time = time.time()
    
    tasks = []
    for i in range(100):
        request_id = f"perf_req_{i}"
        session_id = f"perf_session_{i}"
        
        # 创建异步任务
        async def process_request(req_id, sess_id):
            # 模拟流式响应
            for j in range(5):
                ai_response = create_ai_response(2, f"消息{j}", sess_id)
                adapter.adapt_response(ai_response, req_id)
            
            # 最终响应
            final_response = create_ai_response(0, "完成", sess_id)
            adapter.adapt_response(final_response, req_id)
        
        tasks.append(process_request(request_id, session_id))
    
    # 执行所有任务
    await asyncio.gather(*tasks)
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"  处理100个并发请求耗时: {duration:.2f}秒")
    print(f"  平均每个请求: {duration/100*1000:.2f}毫秒")
    print(f"  吞吐量: {100/duration:.2f}请求/秒")
    
    # 检查最终状态
    status = adapter.get_status()
    print(f"  最终活跃上下文: {status['active_contexts']} (应该为0)")
    
    print("\n✅ 性能测试完成")


def print_json_comparison():
    """打印格式对比"""
    print("\n📋 AI服务器响应格式对比")
    print("=" * 50)
    
    # 原始AI服务器格式
    print("\n🔸 原始AI服务器格式:")
    original_format = {
        "code": 2,
        "data": {
            "sessionId": "session_123",
            "source": 6,
            "message": "你好，我是AI助手",
            "extra": None
        },
        "errorMsg": "",
        "language": "zh-CN"
    }
    print(json.dumps(original_format, ensure_ascii=False, indent=2))
    
    # 适配后的JSON-RPC格式
    print("\n🔸 适配后的JSON-RPC格式:")
    adapter = AIResponseAdapter()
    adapted = adapter.adapt_response(original_format, "req_001")
    print(json.dumps(adapted, ensure_ascii=False, indent=2))
    
    print("\n✅ 格式对比完成")


async def main():
    """主函数"""
    print("🎯 AI响应适配器演示和测试")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 直接测试适配器
    await test_adapter_directly()
    
    # 2. 格式对比
    print_json_comparison()
    
    # 3. 性能测试
    await performance_test()
    
    # 4. 服务器集成测试（需要服务器运行）
    print("\n" + "=" * 60)
    print("注意: 以下测试需要AI服务器运行")
    print("启动命令: python ai_server.py")
    
    user_input = input("\n是否进行服务器集成测试? (y/N): ")
    if user_input.lower() in ['y', 'yes']:
        await test_server_integration()
    else:
        print("跳过服务器集成测试")
    
    print("\n" + "=" * 60)
    print("🎉 所有测试完成!")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    asyncio.run(main())
