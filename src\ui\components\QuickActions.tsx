/**
 * 常用功能区组件
 * 包含快捷操作按钮网格
 */

import {
  Shield,
  Smartphone,
  ArrowRightLeft,
  Building,
  UserPlus,
  Edit,
  Lock,
  PiggyBank,
  FileText,
  AlertTriangle,
  Zap,
  Grid3X3,
  User,
  Search,
  Wallet,
  Target,
  CreditCard,
  Receipt,
} from 'lucide-react';
import React, { useState } from 'react';

import { Card, CardContent } from '../../components/ui/card';
import { WebSDK } from '../../core/WebSDK';
import { generateUUID } from '../../utils/helpers';

interface QuickActionItem {
  id: string; // 添加ID字段
  icon: React.ComponentType<{ size?: number; style?: React.CSSProperties }>;
  text: string;
  color: string;
}

interface BusinessItem {
  id: string;
  name: string;
  icon: React.ComponentType<{ size?: number; style?: React.CSSProperties }>;
  color: string;
  description: string;
}

interface QuickActionsProps {
  onActionClick?: (action: QuickActionItem) => void;
  onBusinessSelect?: (business: string) => void; // 新增：业务选择回调
  layoutMode?: 'standard' | 'care'; // 新增：布局模式
  className?: string;
  style?: React.CSSProperties;
  sdk?: WebSDK; // 添加SDK实例，用于发送事件
}

export const QuickActions: React.FC<QuickActionsProps> = ({
  onActionClick,
  onBusinessSelect,
  layoutMode = 'standard',
  className,
  style,
  sdk, // 接收SDK实例
}) => {
  const [showBusinessModal, setShowBusinessModal] = useState(false);

  // 业务列表数据 - 与AllServices组件保持一致，使用lucide-react图标
  const businessList: BusinessItem[] = [
    {
      id: 'AccountManagement',
      name: '账户管理',
      icon: User,
      color: '#007AFF',
      description: '开卡、挂失、密码管理、账户升降级',
    },
    {
      id: 'AccountInquiry',
      name: '查询业务',
      icon: Search,
      color: '#AF52DE',
      description: '账户查询、交易明细、积分服务',
    },
    {
      id: 'BusinessContract',
      name: '业务签约',
      icon: Smartphone,
      color: '#5AC8FA',
      description: '电子渠道签约、银信通、手机支付',
    },
    {
      id: 'Transfer',
      name: '转账汇款',
      icon: ArrowRightLeft,
      color: '#34C759',
      description: '转账、跨行转账、人行通',
    },
    {
      id: 'SavingsBalance',
      name: '储蓄存款',
      icon: Wallet,
      color: '#FF9500',
      description: '幸福存、定期存款、大额存单',
    },
    {
      id: 'InvestmentFinancialManagement',
      name: '投资理财',
      icon: Target,
      color: '#FF3B30',
      description: '理财产品、基金、风险测评',
    },
    {
      id: 'ConvenienceServices',
      name: '政务民生',
      icon: Building,
      color: '#8E8E93',
      description: '社保服务、水电气费、通讯费',
    },
    {
      id: 'CreditCardBusiness',
      name: '信用卡业务',
      icon: CreditCard,
      color: '#FF2D92',
      description: '额度查询、账单查询、自扣还款',
    },
    {
      id: 'Cash',
      name: '现金业务',
      icon: Receipt,
      color: '#34C759',
      description: '大额存款、小额存款、取款',
    },
  ];

  const quickActions: QuickActionItem[] = [
    { id: 'ElectronicChannels', icon: Smartphone, text: '电子渠道签约', color: '#007AFF' },
    { id: 'SelfServiceCard', icon: UserPlus, text: '开卡', color: '#34C759' },
    { id: 'NonCounterLimit', icon: Lock, text: '账户非柜面限额设置', color: '#FF9500' },
    {
      id: 'HappinessDepositSigningContract',
      icon: PiggyBank,
      text: '幸福存开户',
      color: '#5AC8FA',
    },
    { id: 'TransactionHistoryInquiry', icon: FileText, text: '交易明细查询', color: '#AF52DE' },
    { id: 'PersonalInformation', icon: Edit, text: '个人信息修改', color: '#FF2D92' },
    { id: 'YinTongModify', icon: Shield, text: '银信通修改', color: '#FF3B30' },
    { id: 'RegularOpenAccount', icon: Building, text: '定期开户', color: '#8E8E93' },
    { id: 'BankTransfer', icon: ArrowRightLeft, text: '转账', color: '#34C759' },
    { id: 'ReportLossAndReissue', icon: AlertTriangle, text: '一键挂失补发', color: '#FF9500' },
  ];

  // 在关爱模式下添加全部业务卡片
  const allActions =
    layoutMode === 'care'
      ? [...quickActions, { id: 'AllServices', icon: Grid3X3, text: '全部业务', color: '#6366F1' }]
      : quickActions;

  const handleActionClick = (action: QuickActionItem) => {
    if (action.text === '全部业务') {
      setShowBusinessModal(true);
    } else {
      // 调用原有的回调函数
      onActionClick?.(action);

      // 通过EventBus发送JSON-RPC格式的clientUI事件
      if (sdk && sdk.getStatus().isReady) {
        const eventBus = sdk.getEventBus();
        const requestId = generateUUID();

        const clientUIRequest = {
          jsonrpc: '2.0',
          id: requestId,
          method: 'clientUI',
          params: {
            action: action.id, // 使用常用功能按钮的ID作为action
            name: action.text, // 添加按钮名称（中文）
          },
        };

        // 发送JSON-RPC格式的clientUI请求（用于内部处理）
        eventBus.emit('client:send-request', clientUIRequest);

        // 发送业务按钮点击事件（用于外部监听）
        eventBus.emit('business:button-click', clientUIRequest);

        // 常用功能按钮点击事件已发送
      }
    }
  };

  const handleBusinessClick = (business: BusinessItem) => {
    // 调用原有的回调函数
    onBusinessSelect?.(business.name);

    // 通过EventBus发送JSON-RPC格式的clientUI事件
    if (sdk && sdk.getStatus().isReady) {
      const eventBus = sdk.getEventBus();
      const requestId = generateUUID();

      const clientUIRequest = {
        jsonrpc: '2.0',
        id: requestId,
        method: 'clientUI',
        params: {
          action: business.id, // 使用业务按钮的ID作为action
          name: business.name, // 添加按钮名称（中文）
        },
      };

      // 发送JSON-RPC格式的clientUI请求（用于内部处理）
      eventBus.emit('client:send-request', clientUIRequest);

      // 发送业务按钮点击事件（用于外部监听）
      eventBus.emit('business:button-click', clientUIRequest);

      // 业务按钮点击事件已发送
    }

    setShowBusinessModal(false);
  };

  const handleModalClose = () => {
    setShowBusinessModal(false);
  };

  return (
    <>
      <Card
        className={className}
        style={{
          // 标准模式下由父组件控制flex比例，关爱模式下占满空间
          flex: layoutMode === 'care' ? '1' : style?.flex || '1',
          background: 'rgba(255, 255, 255, 0.8)',
          backdropFilter: 'blur(20px) saturate(180%)',
          border: '1px solid rgba(30, 41, 59, 0.1)',
          borderRadius: '20px',
          boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.06)',
          height: '100%', // 两种模式都占满高度
          // 移除固定最小高度，避免关爱模式下容器溢出
          maxHeight: '100%', // 确保不超出父容器
          ...style,
        }}
      >
        <CardContent
          style={{
            padding: layoutMode === 'care' ? '24px 20px' : '18px 16px', // 普通模式适度减少内边距，增加内容空间
            fontSize: layoutMode === 'care' ? '20px' : '14px',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden', // 防止内容溢出
            boxSizing: 'border-box', // 确保padding计算在内
          }}
        >
          <h3
            style={{
              color: '#1e293b',
              fontSize: layoutMode === 'care' ? '28px' : '22px', // 关爱模式28px，普通模式22px（标题文字）
              fontWeight: '700',
              marginBottom: layoutMode === 'care' ? '20px' : '14px', // 普通模式进一步减少下边距
              margin: `0 0 ${layoutMode === 'care' ? '20px' : '14px'} 0`,
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
            }}
          >
            <Zap size={layoutMode === 'care' ? 32 : 20} style={{ color: '#007AFF' }} />
            常用功能
          </h3>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: layoutMode === 'care' ? 'repeat(3, 1fr)' : 'repeat(5, 1fr)',
              gap: layoutMode === 'care' ? '12px' : '8px', // 普通模式适度增加间距，提升空间利用率
              // 优化网格布局：确保内容不会溢出容器
              gridTemplateRows:
                layoutMode === 'care'
                  ? 'repeat(4, minmax(80px, 1fr))' // 关爱模式：增加最小高度到80px适应大字体
                  : 'repeat(2, minmax(50px, 1fr))', // 普通模式：增加最小高度到50px，更好利用空间
              flex: 1, // 占满剩余空间
              overflow: 'hidden', // 防止撑破容器
              alignContent: 'start', // 两种模式都从顶部开始，避免拉伸导致溢出
            }}
          >
            {allActions.map((item, index) => {
              const IconComponent = item.icon;
              return (
                <button
                  key={index}
                  onClick={() => handleActionClick(item)}
                  className="btn-base hover-lift"
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: layoutMode === 'care' ? '12px' : '4px', // 关爱模式增加间距适应大字体
                    borderRadius: layoutMode === 'care' ? '16px' : '12px', // 普通模式增大圆角
                    padding: layoutMode === 'care' ? '20px 16px' : '10px 8px', // 关爱模式增加内边距适应大字体
                    // 让按钮充分占满网格空间
                    height: '100%',
                    width: '100%',
                    fontSize: layoutMode === 'care' ? '24px' : '18px', // 关爱模式24px，普通模式18px（普通文字）
                    fontWeight: layoutMode === 'care' ? '700' : '600', // 关爱模式使用更粗字重
                    // 实现整个卡片的常驻背景色变化（不是hover效果）
                    background:
                      layoutMode === 'care'
                        ? `linear-gradient(135deg, ${item.color}30, transparent)` // 关爱模式：使用功能色渐变背景，与普通模式保持一致
                        : `linear-gradient(135deg, ${item.color}40, rgba(255, 255, 255, 0.85))`, // 普通模式：大幅增强功能色的渐变背景，提高对比度
                    border:
                      layoutMode === 'care'
                        ? '2px solid rgba(30, 41, 59, 0.2)' // 关爱模式：更粗的边框
                        : '1px solid rgba(30, 41, 59, 0.1)',
                    color: layoutMode === 'care' ? '#000000' : '#1e293b', // 关爱模式：纯黑色文字
                    cursor: 'pointer',
                    transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                    // 确保按钮内容不会溢出
                    boxSizing: 'border-box',
                  }}
                  onMouseEnter={e => {
                    // 保留轻微的浮动效果，但移除变色效果
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
                  }}
                  onMouseLeave={e => {
                    // 恢复原始位置
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                >
                  {/* 图标容器，移除底纹色变化效果 */}
                  <div
                    style={{
                      width: layoutMode === 'care' ? '56px' : '44px', // 图标容器大小
                      height: layoutMode === 'care' ? '56px' : '44px',
                      borderRadius: layoutMode === 'care' ? '16px' : '12px',
                      background: 'rgba(255, 255, 255, 0.6)', // 移除图标底纹色，使用统一的半透明白色背景
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      flexShrink: 0,
                      border: '1px solid rgba(255, 255, 255, 0.8)', // 统一的白色边框
                    }}
                  >
                    <IconComponent
                      size={layoutMode === 'care' ? 36 : 24} // 关爱模式进一步增大图标到36px
                      style={{
                        color: item.color,
                        // 关爱模式下为图标添加更强的对比度
                        filter:
                          layoutMode === 'care' ? 'drop-shadow(0 1px 2px rgba(0,0,0,0.1))' : 'none',
                      }}
                    />
                  </div>
                  <span
                    style={{
                      textAlign: 'center',
                      lineHeight: layoutMode === 'care' ? '1.3' : '1.2', // 关爱模式增加行高
                      fontSize: 'inherit',
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      maxWidth: '100%',
                      // 关爱模式下为文字添加阴影，增强对比度
                      textShadow: layoutMode === 'care' ? '0 1px 2px rgba(0,0,0,0.1)' : 'none',
                      letterSpacing: layoutMode === 'care' ? '0.5px' : 'normal', // 关爱模式增加字间距
                    }}
                  >
                    {item.text}
                  </span>
                </button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* 业务列表弹窗 */}
      {showBusinessModal && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 10000,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '20px',
          }}
          onClick={handleModalClose}
        >
          <div
            style={{
              backgroundColor: '#ffffff',
              borderRadius: '16px',
              padding: '32px',
              maxWidth: '800px',
              width: '100%',
              maxHeight: '80vh',
              overflow: 'auto',
              boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2)',
            }}
            onClick={e => e.stopPropagation()}
          >
            {/* 弹窗标题 */}
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '24px',
                borderBottom: '2px solid #e5e7eb',
                paddingBottom: '16px',
              }}
            >
              <h2
                style={{
                  fontSize: '28px',
                  fontWeight: 'bold',
                  color: '#1f2937',
                  margin: 0,
                }}
              >
                全部业务
              </h2>
              <button
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '32px',
                  cursor: 'pointer',
                  color: '#6b7280',
                  padding: '8px',
                  borderRadius: '8px',
                  transition: 'background-color 0.2s',
                }}
                onClick={handleModalClose}
                onMouseEnter={e => {
                  e.currentTarget.style.backgroundColor = '#f3f4f6';
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                ×
              </button>
            </div>

            {/* 业务列表网格 */}
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '16px',
              }}
            >
              {businessList.map(business => (
                <div
                  key={business.id}
                  style={{
                    backgroundColor: '#f9fafb',
                    borderRadius: '12px',
                    padding: '20px',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    border: '2px solid #e5e7eb',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '16px',
                  }}
                  onClick={() => handleBusinessClick(business)}
                  onMouseEnter={e => {
                    e.currentTarget.style.backgroundColor = '#eff6ff';
                    e.currentTarget.style.borderColor = '#3b82f6';
                  }}
                  onMouseLeave={e => {
                    e.currentTarget.style.backgroundColor = '#f9fafb';
                    e.currentTarget.style.borderColor = '#e5e7eb';
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <business.icon size={32} style={{ color: business.color }} />
                  </div>
                  <div style={{ flex: 1 }}>
                    <div
                      style={{
                        fontSize: '20px',
                        fontWeight: 'bold',
                        color: '#1f2937',
                        marginBottom: '4px',
                      }}
                    >
                      {business.name}
                    </div>
                    <div
                      style={{
                        fontSize: '16px',
                        color: '#6b7280',
                      }}
                    >
                      {business.description}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
};
