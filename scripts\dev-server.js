#!/usr/bin/env node

/**
 * 开发服务器启动脚本
 * 启动Vite开发服务器并自动打开page-manager-test.html
 */

const { spawn } = require('child_process');
const path = require('path');

// 项目根目录
const projectRoot = path.resolve(__dirname, '..');

// 解析命令行参数
const args = process.argv.slice(2);
const useHttps = args.includes('--https');
const useHttp = args.includes('--http');

// 确定协议模式
let protocol = 'https'; // 默认HTTPS（生产环境模拟）
if (useHttp) {
  protocol = 'http';
} else if (useHttps) {
  protocol = 'https';
}

console.log('🚀 启动WebSDK开发服务器...');
console.log(`🔒 协议模式: ${protocol.toUpperCase()}`);
console.log('📄 开发环境首页: /');
console.log('🎯 测试页面: /examples/page-manager-test.html');

if (protocol === 'http') {
  console.log('💡 HTTP模式: 可直接连接localhost后端服务');
} else {
  console.log('💡 HTTPS模式: 模拟生产环境，需要使用IP地址连接后端服务');
}

// 构建Vite命令参数
const viteArgs = ['exec', 'vite', '--host', '0.0.0.0', '--open'];

// 设置环境变量
const env = { ...process.env };
if (protocol === 'http') {
  env.VITE_FORCE_HTTP = 'true';
}

// 启动Vite开发服务器
const viteProcess = spawn('pnpm', viteArgs, {
  cwd: projectRoot,
  stdio: 'inherit',
  shell: true,
  env: env
});

// 处理进程退出
viteProcess.on('close', (code) => {
  console.log(`\n🔚 开发服务器已退出，退出码: ${code}`);
  process.exit(code);
});

// 处理Ctrl+C
process.on('SIGINT', () => {
  console.log('\n🛑 收到中断信号，正在关闭开发服务器...');
  viteProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 收到终止信号，正在关闭开发服务器...');
  viteProcess.kill('SIGTERM');
});
