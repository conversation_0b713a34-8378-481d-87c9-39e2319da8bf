/**
 * WebSDK核心功能测试
 * 测试SDK初始化、连接管理、事件系统、状态管理等核心功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { testUtils } from '../setup';

describe('WebSDK核心功能测试', () => {
  let mockEventBus: any;
  let mockSDK: any;
  let mockConfig: any;

  beforeEach(() => {
    // 创建模拟的EventBus
    mockEventBus = testUtils.createMockEventBus();

    // 创建模拟配置
    mockConfig = {
      hksttUrl: 'ws://localhost:8001',
      aiServerUrl: 'http://localhost:8002',
      debug: true,
    };

    // 创建模拟的SDK实例
    mockSDK = {
      getEventBus: () => mockEventBus,
      getStatus: vi.fn(() => ({ isReady: true, isConnected: true })),
      generateRequestId: () => testUtils.generateTestId('req'),
      sendJsonRpcMessage: vi.fn().mockResolvedValue({ result: 'success' }),
      init: vi.fn().mockResolvedValue(mockSDK),
      destroy: vi.fn(),
      connect: vi.fn().mockResolvedValue(true),
      disconnect: vi.fn(),
      getConfig: () => mockConfig,
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('SDK初始化', () => {
    it('应该能够初始化SDK', async () => {
      // 模拟SDK初始化
      mockSDK.init.mockResolvedValue(mockSDK);
      const sdk = await mockSDK.init(mockConfig);

      // 验证初始化
      expect(mockSDK.init).toHaveBeenCalledWith(mockConfig);
      expect(sdk).toBe(mockSDK);
    });

    it('应该能够获取SDK状态', () => {
      const status = mockSDK.getStatus();

      // 验证状态
      expect(status.isReady).toBe(true);
      expect(status.isConnected).toBe(true);
    });

    it('应该能够获取SDK配置', () => {
      const config = mockSDK.getConfig();

      // 验证配置
      expect(config.hksttUrl).toBe('ws://localhost:8001');
      expect(config.aiServerUrl).toBe('http://localhost:8002');
      expect(config.debug).toBe(true);
    });

    it('应该能够销毁SDK', () => {
      mockSDK.destroy();

      // 验证销毁调用
      expect(mockSDK.destroy).toHaveBeenCalled();
    });
  });

  describe('连接管理', () => {
    it('应该能够建立连接', async () => {
      const connected = await mockSDK.connect();

      // 验证连接
      expect(mockSDK.connect).toHaveBeenCalled();
      expect(connected).toBe(true);
    });

    it('应该能够断开连接', () => {
      mockSDK.disconnect();

      // 验证断开连接
      expect(mockSDK.disconnect).toHaveBeenCalled();
    });

    it('应该能够处理连接状态变化', () => {
      const connectionStates = ['connecting', 'connected', 'disconnected', 'error'];
      const receivedStates: string[] = [];

      // 注册连接状态监听器
      mockEventBus.on('connection:state-changed', (data: any) => {
        receivedStates.push(data.state);
      });

      // 模拟连接状态变化
      connectionStates.forEach(state => {
        mockEventBus.emit('connection:state-changed', {
          state,
          timestamp: Date.now(),
        });
      });

      // 验证状态变化
      expect(receivedStates).toEqual(connectionStates);
    });

    it('应该能够处理连接错误', () => {
      const errorMessage = '连接服务器失败';
      let connectionError: any = null;

      // 注册连接错误监听器
      mockEventBus.on('connection:error', (data: any) => {
        connectionError = data;
      });

      // 模拟连接错误
      mockEventBus.emit('connection:error', {
        error: errorMessage,
        errorCode: 'CONNECTION_FAILED',
        timestamp: Date.now(),
      });

      // 验证错误处理
      expect(connectionError.error).toBe(errorMessage);
      expect(connectionError.errorCode).toBe('CONNECTION_FAILED');
    });
  });

  describe('事件系统', () => {
    it('应该能够注册和触发事件', () => {
      const eventName = 'test:event';
      const eventData = { message: 'test data' };
      let receivedData: any = null;

      // 注册事件监听器
      mockEventBus.on(eventName, (data: any) => {
        receivedData = data;
      });

      // 触发事件
      mockEventBus.emit(eventName, eventData);

      // 验证事件处理
      expect(receivedData).toEqual(eventData);
    });

    it('应该能够移除事件监听器', () => {
      const eventName = 'test:event';
      let callCount = 0;

      const handler = () => {
        callCount++;
      };

      // 注册监听器
      mockEventBus.on(eventName, handler);

      // 触发事件
      mockEventBus.emit(eventName);
      expect(callCount).toBe(1);

      // 移除监听器
      mockEventBus.off(eventName, handler);

      // 再次触发事件
      mockEventBus.emit(eventName);
      expect(callCount).toBe(1); // 应该没有增加
    });

    it('应该能够清理所有事件监听器', () => {
      const events = ['event1', 'event2', 'event3'];
      let totalCalls = 0;

      // 注册多个监听器
      events.forEach(event => {
        mockEventBus.on(event, () => {
          totalCalls++;
        });
      });

      // 触发所有事件
      events.forEach(event => {
        mockEventBus.emit(event);
      });
      expect(totalCalls).toBe(3);

      // 清理所有监听器
      mockEventBus.removeAllListeners();

      // 再次触发事件
      events.forEach(event => {
        mockEventBus.emit(event);
      });
      expect(totalCalls).toBe(3); // 应该没有增加
    });
  });

  describe('JSON-RPC消息处理', () => {
    it('应该能够发送JSON-RPC请求', async () => {
      const method = 'test.method';
      const params = { test: 'data' };
      const requestId = mockSDK.generateRequestId();

      // 发送JSON-RPC请求
      const response = await mockSDK.sendJsonRpcMessage({
        jsonrpc: '2.0',
        method,
        params,
        id: requestId,
      });

      // 验证请求发送
      expect(mockSDK.sendJsonRpcMessage).toHaveBeenCalledWith({
        jsonrpc: '2.0',
        method,
        params,
        id: requestId,
      });

      // 验证响应
      expect(response.result).toBe('success');
    });

    it('应该能够生成唯一的请求ID', () => {
      const id1 = mockSDK.generateRequestId();
      const id2 = mockSDK.generateRequestId();

      // 验证ID唯一性
      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(typeof id2).toBe('string');
    });

    it('应该能够处理JSON-RPC通知', () => {
      const method = 'test.notification';
      const params = { notification: 'data' };
      let receivedNotification: any = null;

      // 注册通知监听器
      mockEventBus.on('jsonrpc:notification', (data: any) => {
        receivedNotification = data;
      });

      // 发送通知
      mockEventBus.emit('jsonrpc:notification', {
        jsonrpc: '2.0',
        method,
        params,
      });

      // 验证通知处理
      expect(receivedNotification.method).toBe(method);
      expect(receivedNotification.params).toEqual(params);
    });
  });

  describe('状态管理', () => {
    it('应该能够跟踪SDK状态', () => {
      const states = [
        { isReady: false, isConnected: false },
        { isReady: false, isConnected: true },
        { isReady: true, isConnected: true },
        { isReady: true, isConnected: false },
      ];

      states.forEach(state => {
        mockSDK.getStatus.mockReturnValue(state);
        const currentStatus = mockSDK.getStatus();
        expect(currentStatus).toEqual(state);
      });
    });

    it('应该能够处理状态变化事件', () => {
      const statusChanges = [
        { isReady: true, isConnected: true },
        { isReady: true, isConnected: false },
        { isReady: false, isConnected: false },
      ];
      const receivedStatuses: any[] = [];

      // 注册状态变化监听器
      mockEventBus.on('sdk:status-changed', (data: any) => {
        receivedStatuses.push(data.status);
      });

      // 模拟状态变化
      statusChanges.forEach(status => {
        mockEventBus.emit('sdk:status-changed', {
          status,
          timestamp: Date.now(),
        });
      });

      // 验证状态变化
      expect(receivedStatuses).toEqual(statusChanges);
    });
  });

  describe('错误处理', () => {
    it('应该能够处理SDK错误', () => {
      const errorMessage = 'SDK内部错误';
      let sdkError: any = null;

      // 注册错误监听器
      mockEventBus.on('sdk:error', (data: any) => {
        sdkError = data;
      });

      // 模拟SDK错误
      mockEventBus.emit('sdk:error', {
        error: errorMessage,
        errorCode: 'SDK_INTERNAL_ERROR',
        timestamp: Date.now(),
      });

      // 验证错误处理
      expect(sdkError.error).toBe(errorMessage);
      expect(sdkError.errorCode).toBe('SDK_INTERNAL_ERROR');
    });

    it('应该能够处理网络错误', () => {
      const errorMessage = '网络连接中断';
      let networkError: any = null;

      // 注册网络错误监听器
      mockEventBus.on('network:error', (data: any) => {
        networkError = data;
      });

      // 模拟网络错误
      mockEventBus.emit('network:error', {
        error: errorMessage,
        errorCode: 'NETWORK_DISCONNECTED',
        timestamp: Date.now(),
      });

      // 验证网络错误处理
      expect(networkError.error).toBe(errorMessage);
      expect(networkError.errorCode).toBe('NETWORK_DISCONNECTED');
    });
  });
});
