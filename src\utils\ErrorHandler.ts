/**
 * 简化的错误处理工具
 */

/**
 * 错误类型
 */
export enum ErrorType {
  NETWORK = 'network',
  VALIDATION = 'validation',
  TIMEOUT = 'timeout',
  SYSTEM = 'system',
  USER = 'user',
}

/**
 * SDK错误类
 */
export class SDKError extends Error {
  public readonly type: ErrorType;
  public readonly timestamp: number;
  public readonly requestId?: string;

  constructor(message: string, type: ErrorType = ErrorType.SYSTEM, requestId?: string) {
    super(message);
    this.name = 'SDKError';
    this.type = type;
    this.timestamp = Date.now();

    if (requestId) {
      this.requestId = requestId;
    }

    // 确保错误堆栈正确
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, SDKError);
    }
  }

  /**
   * 转换为简单对象
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      type: this.type,
      timestamp: this.timestamp,
      requestId: this.requestId,
    };
  }
}

/**
 * 错误处理器
 */
export class ErrorHandler {
  /**
   * 创建网络错误
   */
  static createNetworkError(message: string, requestId?: string): SDKError {
    return new SDKError(message, ErrorType.NETWORK, requestId);
  }

  /**
   * 创建连接错误
   */
  static createConnectionError(message: string, cause?: unknown, requestId?: string): SDKError {
    const errorMessage = cause instanceof Error ? `${message}: ${cause.message}` : message;
    return new SDKError(errorMessage, ErrorType.NETWORK, requestId);
  }

  /**
   * 创建验证错误
   */
  static createValidationError(message: string, requestId?: string): SDKError {
    return new SDKError(message, ErrorType.VALIDATION, requestId);
  }

  /**
   * 创建超时错误
   */
  static createTimeoutError(message: string, requestId?: string): SDKError {
    return new SDKError(message, ErrorType.TIMEOUT, requestId);
  }

  /**
   * 创建系统错误
   */
  static createSystemError(message: string, requestId?: string): SDKError {
    return new SDKError(message, ErrorType.SYSTEM, requestId);
  }

  /**
   * 创建用户错误
   */
  static createUserError(message: string, requestId?: string): SDKError {
    return new SDKError(message, ErrorType.USER, requestId);
  }

  /**
   * 包装原生错误
   */
  static wrapError(error: Error, type: ErrorType = ErrorType.SYSTEM, requestId?: string): SDKError {
    if (error instanceof SDKError) {
      return error;
    }

    return new SDKError(error.message, type, requestId);
  }

  /**
   * 判断是否为SDK错误
   */
  static isSDKError(error: unknown): error is SDKError {
    return error instanceof SDKError;
  }

  /**
   * 安全地获取错误消息
   */
  static getErrorMessage(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }

    if (typeof error === 'string') {
      return error;
    }

    return '未知错误';
  }

  /**
   * 格式化错误信息
   */
  static formatError(error: unknown): {
    message: string;
    type: string;
    timestamp: number;
    requestId?: string;
  } {
    if (error instanceof SDKError) {
      const result: {
        message: string;
        type: string;
        timestamp: number;
        requestId?: string;
      } = {
        message: error.message,
        type: error.type,
        timestamp: error.timestamp,
      };

      if (error.requestId) {
        result.requestId = error.requestId;
      }

      return result;
    }

    return {
      message: ErrorHandler.getErrorMessage(error),
      type: ErrorType.SYSTEM,
      timestamp: Date.now(),
    };
  }
}
