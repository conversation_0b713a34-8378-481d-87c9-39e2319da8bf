# CosyVoice WebSocket API 文档

## 1. API概述

CosyVoice WebSocket API 是一个实时语音合成服务接口，支持通过 WebSocket 协议进行实时文本到语音的转换。该 API 提供流式音频数据传输，支持多种音频格式输出，并具备指令式语音合成功能。

### 主要功能
- 实时文本到语音合成
- 流式音频数据传输
- 支持指令式语音控制（如音色、语调调整）
- 多种音频格式支持（PCM、WAV、MP3）
- 心跳检测机制
- 客户端状态管理

## 2. 连接方式

### WebSocket 连接URL格式
```
ws://[服务器地址]:[端口]/ws/tts/[客户端ID]
```

### 连接参数
- **服务器地址**: WebSocket 服务器的 IP 地址或域名
- **端口**: 服务器监听端口（默认 8000）
- **客户端ID**: 唯一标识客户端的字符串，格式为 `client_` + 9位随机字符

### 连接示例
```javascript
// 生成客户端ID
function generateClientId() {
    return 'client_' + Math.random().toString(36).substr(2, 9);
}

// 建立连接
const clientId = generateClientId();
const serverUrl = `ws://*************:8000/ws/tts/${clientId}`;
const websocket = new WebSocket(serverUrl);
```

## 3. 消息格式

所有消息均采用 JSON 格式进行传输。

### 发送消息格式
```json
{
    "type": "消息类型",
    "params": {
        // 具体参数
    },
    "timestamp": "时间戳（可选）"
}
```

### 接收消息格式
```json
{
    "type": "消息类型",
    "status": "状态（可选）",
    "message": "消息内容",
    "data": {
        // 具体数据
    }
}
```

## 4. 方法列表

### 4.1 语音合成 (synthesize)

**发送消息**:
```json
{
    "type": "synthesize",
    "params": {
        "tts_text": "要合成的文本内容",
        "instruct_text": "指令文本，如：用温柔的声音说话"
    }
}
```

**参数说明**:
- `tts_text` (string, 必需): 要合成语音的文本内容
- `instruct_text` (string, 必需): 语音合成的指令文本，用于控制语音特征

**返回消息**:
- 状态消息: `{"type": "status", "status": "started", "message": "开始合成"}`
- 音频数据: `{"type": "audio_chunk", ...}`
- 完成消息: `{"type": "status", "status": "completed", "message": "合成完成"}`

### 4.2 心跳检测 (ping)

**发送消息**:
```json
{
    "type": "ping",
    "timestamp": "2024-01-01T12:00:00.000Z"
}
```

**返回消息**:
```json
{
    "type": "pong",
    "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## 5. 事件处理

### 5.1 连接事件

```javascript
websocket.onopen = function(event) {
    console.log('WebSocket连接成功');
    // 连接成功后的处理逻辑
};
```

### 5.2 消息接收事件

```javascript
websocket.onmessage = function(event) {
    try {
        const message = JSON.parse(event.data);
        handleMessage(message);
    } catch (e) {
        console.error('解析消息失败:', e);
    }
};
```

### 5.3 连接关闭事件

```javascript
websocket.onclose = function(event) {
    console.log('WebSocket连接已关闭');
    // 清理资源和状态重置
};
```

### 5.4 错误处理事件

```javascript
websocket.onerror = function(error) {
    console.error('WebSocket错误:', error);
    // 错误处理逻辑
};
```

## 6. 音频数据处理

### 6.1 音频块消息格式

```json
{
    "type": "audio_chunk",
    "chunk_id": 1,
    "is_final": false,
    "audio_data": "base64编码的音频数据",
    "sample_rate": 24000,
    "audio_format": "wav",
    "mime_type": "audio/wav"
}
```

### 6.2 音频数据字段说明

- `chunk_id` (number): 音频块序号
- `is_final` (boolean): 是否为最后一个音频块
- `audio_data` (string): Base64编码的音频数据
- `sample_rate` (number): 采样率，默认 24000Hz
- `audio_format` (string): 音频格式 (pcm/wav/mp3)
- `mime_type` (string): MIME类型

### 6.3 音频数据处理示例

```javascript
function handleAudioChunk(message) {
    const audioDataB64 = message.audio_data;
    const audioBytes = atob(audioDataB64); // Base64解码

    // 存储音频块
    audioData.chunks.push(audioBytes);

    if (message.is_final) {
        // 合并所有音频块并创建播放器
        createAudioPlayer();
    }
}
```

## 7. 完整使用示例

### 7.1 基础连接和语音合成示例

```javascript
class CosyVoiceClient {
    constructor(serverUrl) {
        this.serverUrl = serverUrl;
        this.websocket = null;
        this.clientId = null;
        this.audioData = null;
        this.isConnected = false;
    }

    // 生成客户端ID
    generateClientId() {
        return 'client_' + Math.random().toString(36).substr(2, 9);
    }

    // 连接到服务器
    connect() {
        return new Promise((resolve, reject) => {
            this.clientId = this.generateClientId();
            const fullUrl = `${this.serverUrl}${this.clientId}`;

            this.websocket = new WebSocket(fullUrl);

            this.websocket.onopen = (event) => {
                this.isConnected = true;
                console.log('连接成功');
                resolve(event);
            };

            this.websocket.onmessage = (event) => {
                this.handleMessage(JSON.parse(event.data));
            };

            this.websocket.onclose = (event) => {
                this.isConnected = false;
                console.log('连接已关闭');
            };

            this.websocket.onerror = (error) => {
                this.isConnected = false;
                console.error('连接错误:', error);
                reject(error);
            };
        });
    }

    // 发送消息
    sendMessage(message) {
        if (!this.isConnected || !this.websocket) {
            throw new Error('WebSocket未连接');
        }

        this.websocket.send(JSON.stringify(message));
    }

    // 语音合成
    synthesize(ttsText, instructText) {
        const message = {
            type: 'synthesize',
            params: {
                tts_text: ttsText,
                instruct_text: instructText
            }
        };

        this.sendMessage(message);

        // 初始化音频数据容器
        this.audioData = {
            chunks: [],
            sampleRate: 24000,
            format: 'wav',
            mimeType: 'audio/wav'
        };
    }

    // 处理接收到的消息
    handleMessage(message) {
        switch (message.type) {
            case 'connected':
                console.log('服务器确认连接:', message.message);
                break;

            case 'status':
                console.log('状态更新:', message.status, message.message);
                break;

            case 'audio_chunk':
                this.handleAudioChunk(message);
                break;

            case 'error':
                console.error('服务器错误:', message.message);
                break;

            case 'pong':
                console.log('心跳响应收到');
                break;
        }
    }

    // 处理音频块
    handleAudioChunk(message) {
        if (message.audio_data) {
            const audioBytes = atob(message.audio_data);
            this.audioData.chunks.push(audioBytes);
        }

        // 更新音频信息
        if (message.sample_rate) this.audioData.sampleRate = message.sample_rate;
        if (message.audio_format) this.audioData.format = message.audio_format;
        if (message.mime_type) this.audioData.mimeType = message.mime_type;

        if (message.is_final) {
            console.log('音频接收完成');
            this.onAudioComplete(this.audioData);
        }
    }

    // 音频接收完成回调（需要用户实现）
    onAudioComplete(audioData) {
        // 用户可以重写此方法来处理完整的音频数据
        console.log('音频数据接收完成，共', audioData.chunks.length, '个块');
    }

    // 发送心跳
    ping() {
        const message = {
            type: 'ping',
            timestamp: new Date().toISOString()
        };
        this.sendMessage(message);
    }

    // 断开连接
    disconnect() {
        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
            this.isConnected = false;
        }
    }
}

// 使用示例
async function example1() {
    const client = new CosyVoiceClient('ws://*************:8000/ws/tts/');

    try {
        // 连接到服务器
        await client.connect();

        // 重写音频完成回调
        client.onAudioComplete = function(audioData) {
            console.log('收到音频数据:', audioData);
            // 这里可以处理音频数据，如播放或下载
        };

        // 开始语音合成
        client.synthesize('你好，我是CosyVoice语音合成系统！', '用温柔的声音说话');

    } catch (error) {
        console.error('连接失败:', error);
    }
}
```

### 7.2 音频下载和播放示例

```javascript
// 音频处理工具类
class AudioProcessor {
    // 合并音频块并创建下载链接
    static createDownloadLink(audioData, filename) {
        const totalLength = audioData.chunks.reduce((sum, chunk) => sum + chunk.length, 0);
        const combined = new Uint8Array(totalLength);
        let offset = 0;

        // 合并所有音频块
        for (const chunk of audioData.chunks) {
            const bytes = new Uint8Array(chunk.length);
            for (let i = 0; i < chunk.length; i++) {
                bytes[i] = chunk.charCodeAt(i) & 0xFF;
            }
            combined.set(bytes, offset);
            offset += bytes.length;
        }

        // 创建Blob和下载链接
        const blob = new Blob([combined], { type: audioData.mimeType });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename || `audio_${Date.now()}.${audioData.format}`;

        return { url, element: a, blob };
    }

    // 播放WAV音频
    static playWavAudio(audioData) {
        if (audioData.format !== 'wav') {
            throw new Error('只支持播放WAV格式音频');
        }

        const { blob, url } = this.createDownloadLink(audioData);
        const audio = new Audio(url);

        return new Promise((resolve, reject) => {
            audio.onloadeddata = () => {
                console.log('音频加载成功');
            };

            audio.onerror = (e) => {
                URL.revokeObjectURL(url);
                reject(new Error('音频播放失败: ' + e.message));
            };

            audio.onended = () => {
                URL.revokeObjectURL(url);
                resolve();
            };

            audio.play().catch(e => {
                URL.revokeObjectURL(url);
                reject(e);
            });
        });
    }
}

// 使用示例
async function example2() {
    const client = new CosyVoiceClient('ws://*************:8000/ws/tts/');

    client.onAudioComplete = async function(audioData) {
        try {
            // 下载音频文件
            const { element } = AudioProcessor.createDownloadLink(
                audioData,
                `cosyvoice_${Date.now()}.${audioData.format}`
            );
            document.body.appendChild(element);
            element.click();
            document.body.removeChild(element);

            // 如果是WAV格式，还可以直接播放
            if (audioData.format === 'wav') {
                await AudioProcessor.playWavAudio(audioData);
                console.log('音频播放完成');
            }

        } catch (error) {
            console.error('音频处理失败:', error);
        }
    };

    await client.connect();
    client.synthesize('这是一个测试音频', '用清晰的声音朗读');
}
```

### 7.3 带重连机制的完整示例

```javascript
class RobustCosyVoiceClient extends CosyVoiceClient {
    constructor(serverUrl, options = {}) {
        super(serverUrl);
        this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
        this.reconnectDelay = options.reconnectDelay || 3000;
        this.reconnectAttempts = 0;
        this.autoReconnect = options.autoReconnect !== false;
        this.heartbeatInterval = options.heartbeatInterval || 30000;
        this.heartbeatTimer = null;
    }

    async connect() {
        try {
            await super.connect();
            this.reconnectAttempts = 0;
            this.startHeartbeat();
            return true;
        } catch (error) {
            if (this.autoReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
                this.reconnectAttempts++;
                console.log(`连接失败，${this.reconnectDelay/1000}秒后进行第${this.reconnectAttempts}次重连...`);

                await new Promise(resolve => setTimeout(resolve, this.reconnectDelay));
                return this.connect();
            } else {
                throw error;
            }
        }
    }

    startHeartbeat() {
        this.stopHeartbeat();
        this.heartbeatTimer = setInterval(() => {
            if (this.isConnected) {
                this.ping();
            }
        }, this.heartbeatInterval);
    }

    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }

    disconnect() {
        this.stopHeartbeat();
        super.disconnect();
    }
}

// 使用示例
async function example3() {
    const client = new RobustCosyVoiceClient('ws://*************:8000/ws/tts/', {
        maxReconnectAttempts: 3,
        reconnectDelay: 2000,
        heartbeatInterval: 30000
    });

    client.onAudioComplete = function(audioData) {
        console.log(`收到${audioData.format}格式音频，共${audioData.chunks.length}个块`);

        // 自动下载
        const { element } = AudioProcessor.createDownloadLink(audioData);
        document.body.appendChild(element);
        element.click();
        document.body.removeChild(element);
    };

    try {
        await client.connect();
        console.log('连接成功，开始合成...');

        // 批量合成多个文本
        const texts = [
            { text: '欢迎使用CosyVoice', instruct: '用欢快的声音说话' },
            { text: '这是第二段测试文本', instruct: '用正常的声音说话' },
            { text: '感谢您的使用', instruct: '用温和的声音说话' }
        ];

        for (let i = 0; i < texts.length; i++) {
            console.log(`开始合成第${i+1}段文本...`);
            client.synthesize(texts[i].text, texts[i].instruct);

            // 等待当前合成完成再进行下一个
            await new Promise(resolve => {
                const originalCallback = client.onAudioComplete;
                client.onAudioComplete = function(audioData) {
                    originalCallback.call(this, audioData);
                    resolve();
                };
            });
        }

    } catch (error) {
        console.error('操作失败:', error);
    } finally {
        client.disconnect();
    }
}
```

## 8. 错误处理

### 8.1 常见错误类型

| 错误类型 | 描述 | 处理建议 |
|---------|------|---------|
| 连接失败 | 无法建立WebSocket连接 | 检查服务器地址和端口，确认服务器运行状态 |
| 认证失败 | 客户端ID无效或重复 | 重新生成客户端ID并重连 |
| 参数错误 | 合成参数不正确 | 检查tts_text和instruct_text参数 |
| 服务器错误 | 服务器内部处理错误 | 查看错误消息，必要时重试 |
| 网络中断 | 连接意外断开 | 实现自动重连机制 |
| 音频解码失败 | Base64音频数据损坏 | 检查网络稳定性，重新请求 |

### 8.2 错误处理最佳实践

```javascript
class ErrorHandler {
    static handleWebSocketError(error, client) {
        console.error('WebSocket错误:', error);

        // 根据错误类型采取不同处理策略
        if (error.code === 1006) {
            // 连接异常关闭
            console.log('连接异常关闭，尝试重连...');
            setTimeout(() => client.connect(), 3000);
        } else if (error.code === 1002) {
            // 协议错误
            console.error('协议错误，请检查消息格式');
        }
    }

    static handleSynthesisError(message) {
        const errorCode = message.code || 'UNKNOWN';
        const errorMessage = message.message || '未知错误';

        switch (errorCode) {
            case 'INVALID_TEXT':
                console.error('文本参数无效:', errorMessage);
                break;
            case 'SYNTHESIS_FAILED':
                console.error('合成失败:', errorMessage);
                break;
            case 'SERVER_BUSY':
                console.warn('服务器繁忙，请稍后重试');
                break;
            default:
                console.error('合成错误:', errorMessage);
        }
    }
}
```

### 8.3 超时处理

```javascript
class TimeoutManager {
    constructor(client) {
        this.client = client;
        this.synthesisTimeout = null;
        this.connectionTimeout = null;
    }

    // 设置合成超时
    setSynthesisTimeout(duration = 60000) {
        this.clearSynthesisTimeout();
        this.synthesisTimeout = setTimeout(() => {
            console.warn('合成超时，停止当前操作');
            this.client.disconnect();
        }, duration);
    }

    // 清除合成超时
    clearSynthesisTimeout() {
        if (this.synthesisTimeout) {
            clearTimeout(this.synthesisTimeout);
            this.synthesisTimeout = null;
        }
    }

    // 设置连接超时
    setConnectionTimeout(duration = 10000) {
        this.clearConnectionTimeout();
        this.connectionTimeout = setTimeout(() => {
            console.warn('连接超时');
            this.client.disconnect();
        }, duration);
    }

    // 清除连接超时
    clearConnectionTimeout() {
        if (this.connectionTimeout) {
            clearTimeout(this.connectionTimeout);
            this.connectionTimeout = null;
        }
    }
}
```

## 9. 最佳实践

### 9.1 连接管理

1. **使用唯一客户端ID**: 确保每个客户端使用唯一的ID，避免冲突
2. **实现重连机制**: 网络不稳定时自动重连，但要限制重连次数
3. **心跳检测**: 定期发送ping消息保持连接活跃
4. **优雅断开**: 页面关闭时主动断开WebSocket连接

```javascript
// 推荐的连接管理模式
class ConnectionManager {
    constructor(serverUrl) {
        this.serverUrl = serverUrl;
        this.client = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
    }

    async ensureConnection() {
        if (!this.client || !this.client.isConnected) {
            this.client = new RobustCosyVoiceClient(this.serverUrl);
            await this.client.connect();
        }
        return this.client;
    }

    async synthesizeWithRetry(text, instruct, maxRetries = 3) {
        for (let i = 0; i < maxRetries; i++) {
            try {
                const client = await this.ensureConnection();
                client.synthesize(text, instruct);
                return;
            } catch (error) {
                console.warn(`合成尝试 ${i + 1} 失败:`, error);
                if (i === maxRetries - 1) throw error;
                await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
            }
        }
    }
}
```

### 9.2 性能优化

1. **音频数据缓存**: 避免重复合成相同文本
2. **分块处理**: 长文本分段合成，提高响应速度
3. **内存管理**: 及时清理音频数据，避免内存泄漏
4. **并发控制**: 限制同时进行的合成任务数量

```javascript
// 音频缓存管理
class AudioCache {
    constructor(maxSize = 50) {
        this.cache = new Map();
        this.maxSize = maxSize;
    }

    getKey(text, instruct) {
        return `${text}|${instruct}`;
    }

    get(text, instruct) {
        const key = this.getKey(text, instruct);
        return this.cache.get(key);
    }

    set(text, instruct, audioData) {
        const key = this.getKey(text, instruct);

        // 如果缓存已满，删除最旧的条目
        if (this.cache.size >= this.maxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }

        this.cache.set(key, {
            audioData,
            timestamp: Date.now()
        });
    }

    clear() {
        this.cache.clear();
    }
}
```

### 9.3 安全考虑

1. **输入验证**: 验证文本长度和内容，防止恶意输入
2. **频率限制**: 实现客户端请求频率限制
3. **连接限制**: 限制单个客户端的连接数量
4. **数据清理**: 及时清理敏感的音频数据

```javascript
// 输入验证工具
class InputValidator {
    static validateText(text) {
        if (!text || typeof text !== 'string') {
            throw new Error('文本不能为空且必须是字符串');
        }

        if (text.length > 1000) {
            throw new Error('文本长度不能超过1000字符');
        }

        // 检查是否包含特殊字符或恶意内容
        const dangerousPatterns = [/<script/i, /javascript:/i, /on\w+=/i];
        for (const pattern of dangerousPatterns) {
            if (pattern.test(text)) {
                throw new Error('文本包含不安全内容');
            }
        }

        return true;
    }

    static validateInstruct(instruct) {
        if (!instruct || typeof instruct !== 'string') {
            throw new Error('指令文本不能为空且必须是字符串');
        }

        if (instruct.length > 100) {
            throw new Error('指令文本长度不能超过100字符');
        }

        return true;
    }
}
```

### 9.4 调试和监控

1. **详细日志**: 记录所有关键操作和错误信息
2. **性能监控**: 监控合成时间和音频质量
3. **状态追踪**: 跟踪连接状态和合成进度
4. **错误统计**: 统计错误类型和频率

```javascript
// 监控工具
class Monitor {
    constructor() {
        this.stats = {
            connections: 0,
            synthesisRequests: 0,
            successfulSynthesis: 0,
            errors: {},
            averageResponseTime: 0
        };
    }

    recordConnection() {
        this.stats.connections++;
    }

    recordSynthesisStart() {
        this.stats.synthesisRequests++;
        return Date.now();
    }

    recordSynthesisComplete(startTime) {
        const duration = Date.now() - startTime;
        this.stats.successfulSynthesis++;

        // 更新平均响应时间
        const total = this.stats.averageResponseTime * (this.stats.successfulSynthesis - 1) + duration;
        this.stats.averageResponseTime = total / this.stats.successfulSynthesis;
    }

    recordError(errorType) {
        this.stats.errors[errorType] = (this.stats.errors[errorType] || 0) + 1;
    }

    getStats() {
        return { ...this.stats };
    }

    reset() {
        this.stats = {
            connections: 0,
            synthesisRequests: 0,
            successfulSynthesis: 0,
            errors: {},
            averageResponseTime: 0
        };
    }
}
```

## 10. 常见问题解答

### Q1: 为什么连接失败？
**A**: 检查以下几点：
- 服务器是否正在运行
- WebSocket URL是否正确
- 网络连接是否正常
- 防火墙是否阻止了连接

### Q2: 音频播放没有声音？
**A**: 可能的原因：
- 音频格式不支持（建议使用WAV格式）
- 音频数据损坏（检查网络传输）
- 浏览器音频权限被禁用
- 音频解码失败

### Q3: 如何处理长文本合成？
**A**: 建议将长文本分段处理：
```javascript
function splitText(text, maxLength = 200) {
    const sentences = text.split(/[。！？.!?]/);
    const chunks = [];
    let currentChunk = '';

    for (const sentence of sentences) {
        if (currentChunk.length + sentence.length > maxLength) {
            if (currentChunk) chunks.push(currentChunk.trim());
            currentChunk = sentence;
        } else {
            currentChunk += sentence;
        }
    }

    if (currentChunk) chunks.push(currentChunk.trim());
    return chunks;
}
```

### Q4: 如何优化合成速度？
**A**: 几个优化建议：
- 使用缓存避免重复合成
- 预连接WebSocket
- 分段并行处理
- 选择合适的音频格式

### Q5: 支持哪些音频格式？
**A**: 目前支持：
- **PCM**: 原始音频数据，需要专业工具播放
- **WAV**: 标准音频格式，支持直接播放
- **MP3**: 压缩音频格式，文件较小

---

## 技术支持

如有问题或建议，请联系开发团队或查看项目文档。

**文档版本**: 1.0
**最后更新**: 2024年1月
**适用版本**: CosyVoice WebSocket API v1.0+
