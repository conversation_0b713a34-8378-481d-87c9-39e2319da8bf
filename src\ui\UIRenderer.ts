/**
 * UI渲染器接口
 * 将DOM操作从业务逻辑中分离，提高可测试性和可扩展性
 */

/**
 * 数字人页面配置
 */
export interface DigitalHumanPageConfig {
  /** 页面标题 */
  title?: string;
  /** 欢迎消息 */
  welcomeMessage?: string;
  /** 自定义样式 */
  customStyles?: Record<string, string>;
  /** 是否显示返回按钮 */
  showBackButton?: boolean;
}

/**
 * 消息类型
 */
export interface ChatMessage {
  /** 消息ID */
  id: string;
  /** 消息类型 */
  type: 'user' | 'ai';
  /** 消息内容 */
  content: string;
  /** 时间戳 */
  timestamp: number;
  /** 是否为语音消息 */
  isVoice?: boolean;
  /** 请求ID（用于流式响应） */
  requestId?: string;
  /** 是否完成（用于流式响应） */
  isComplete?: boolean;
}

/**
 * UI渲染器接口
 */
export interface UIRenderer {
  /**
   * 创建数字人页面
   * @param config 页面配置
   * @returns 页面容器元素
   */
  createDigitalHumanPage(config?: DigitalHumanPageConfig): HTMLElement;

  /**
   * 显示数字人页面
   * @param container 页面容器
   */
  showDigitalHumanPage(container: HTMLElement): void;

  /**
   * 隐藏数字人页面
   * @param container 页面容器
   */
  hideDigitalHumanPage(container: HTMLElement): void;

  /**
   * 添加聊天消息
   * @param container 页面容器
   * @param message 消息内容
   */
  addChatMessage(container: HTMLElement, message: ChatMessage): void;

  /**
   * 更新流式消息
   * @param container 页面容器
   * @param requestId 请求ID
   * @param content 新增内容
   * @param isComplete 是否完成
   */
  updateStreamMessage(
    container: HTMLElement,
    requestId: string,
    content: string,
    isComplete: boolean
  ): void;

  /**
   * 清空聊天内容
   * @param container 页面容器
   * @param keepWelcome 是否保留欢迎消息
   */
  clearChatContent(container: HTMLElement, keepWelcome?: boolean): void;

  /**
   * 设置返回按钮点击处理器
   * @param container 页面容器
   * @param handler 点击处理器
   */
  setBackButtonHandler(container: HTMLElement, handler: () => void): void;

  /**
   * 销毁页面
   * @param container 页面容器
   */
  destroyPage(container: HTMLElement): void;

  /**
   * 获取聊天内容容器
   * @param container 页面容器
   * @returns 聊天内容容器
   */
  getChatContentContainer(container: HTMLElement): HTMLElement | null;
}

/**
 * 默认UI渲染器实现
 */
export class DefaultUIRenderer implements UIRenderer {
  private streamMessageElements = new Map<string, HTMLElement>();

  /**
   * 创建数字人页面
   */
  public createDigitalHumanPage(config: DigitalHumanPageConfig = {}): HTMLElement {
    const { title = '数字人助手', showBackButton = true } = config;

    const container = document.createElement('div');
    container.id = 'digital-human-page-container';
    container.className = 'digital-human-page-container';

    // 设置基础样式
    Object.assign(container.style, {
      display: 'none',
      position: 'fixed',
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      backgroundColor: '#ffffff',
      zIndex: '9999',
      fontFamily: 'Arial, sans-serif',
      transition: 'opacity 0.3s ease',
    });

    // 创建页面内容
    container.innerHTML = `
      <div class="digital-human-page" style="
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: row;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      ">
        <!-- 左侧数字人视频区域 -->
        <div class="digital-human-video-section" style="
          width: 50%;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px;
          position: relative;
        ">
          ${
            showBackButton
              ? `
          <button id="digital-human-back-button" class="back-button" style="
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
            padding: 12px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
          ">
            ← 返回
          </button>`
              : ''
          }

          <div class="video-container" style="
            width: 400px;
            height: 400px;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            background: #000;
            position: relative;
          ">
            <simple-digital-human
              id="digital-human-video-player"
              auto-play="true"
              loop="true"
              muted="true">
            </simple-digital-human>
          </div>

          <div class="human-info" style="
            margin-top: 20px;
            text-align: center;
            color: white;
          ">
            <h2 style="
              margin: 0 0 8px 0;
              font-size: 24px;
              font-weight: 600;
              text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            ">${title}</h2>
            <p style="
              margin: 0;
              font-size: 14px;
              opacity: 0.9;
              text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            ">智能语音助手</p>
          </div>
        </div>

        <!-- 右侧聊天交互区域 -->
        <div class="chat-interaction-section" style="
          width: 50%;
          height: 100%;
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(10px);
          display: flex;
          flex-direction: column;
        ">
          <header class="chat-header" style="
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background: rgba(255, 255, 255, 0.8);
          ">
            <h3 style="
              margin: 0;
              font-size: 18px;
              font-weight: 600;
              color: #333;
            ">智能对话</h3>
            <p style="
              margin: 8px 0 0 0;
              font-size: 14px;
              color: #666;
            ">请说出您的需求，我会为您提供帮助</p>
          </header>

          <main class="chat-container" style="
            flex: 1;
            padding: 20px;
            overflow-y: auto;
          ">
            <div id="digital-human-chat-content" class="chat-content" style="
              display: flex;
              flex-direction: column;
              gap: 16px;
              height: 100%;
            ">
              <!-- 聊天消息将在这里动态添加 -->
            </div>
          </main>

          <footer class="chat-footer" style="
            padding: 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            background: rgba(255, 255, 255, 0.8);
          ">
            <div class="voice-controls" style="
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 16px;
            ">
              <button id="voice-input-button" style="
                width: 60px;
                height: 60px;
                border-radius: 50%;
                border: none;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                font-size: 24px;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
              ">
                🎤
              </button>
              <span style="
                font-size: 14px;
                color: #666;
              ">点击开始语音对话</span>
            </div>
          </footer>
        </div>
      </div>
    `;

    // 添加返回按钮悬停效果
    if (showBackButton) {
      const backButton = container.querySelector('#digital-human-back-button') as HTMLElement;
      if (backButton) {
        backButton.addEventListener('mouseenter', () => {
          backButton.style.backgroundColor = 'rgba(255, 255, 255, 0.3)';
          backButton.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
        });

        backButton.addEventListener('mouseleave', () => {
          backButton.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
          backButton.style.boxShadow = 'none';
        });
      }
    }

    return container;
  }

  /**
   * 显示数字人页面
   */
  public showDigitalHumanPage(container: HTMLElement): void {
    container.style.display = 'block';
    // 添加到body（如果还没有添加）
    if (!container.parentElement) {
      document.body.appendChild(container);
    }
  }

  /**
   * 隐藏数字人页面
   */
  public hideDigitalHumanPage(container: HTMLElement): void {
    container.style.display = 'none';
  }

  /**
   * 添加聊天消息
   */
  public addChatMessage(container: HTMLElement, message: ChatMessage): void {
    const chatContainer = this.getChatContentContainer(container);
    if (!chatContainer) {
      // 聊天容器不存在，静默返回
      return;
    }

    // 调试信息已移除，避免生产环境输出

    // 如果是流式响应且有相同的requestId，累积到现有消息
    if (
      message.requestId &&
      !message.isComplete &&
      this.streamMessageElements.has(message.requestId)
    ) {
      this.updateStreamMessage(container, message.requestId, message.content, false);
      return;
    }

    const messageElement = this.createMessageElement(message);
    chatContainer.appendChild(messageElement);
    chatContainer.scrollTop = chatContainer.scrollHeight;

    // 如果是流式响应的开始，保存引用
    if (message.requestId && !message.isComplete) {
      this.streamMessageElements.set(message.requestId, messageElement);
    }
  }

  /**
   * 更新流式消息
   */
  public updateStreamMessage(
    container: HTMLElement,
    requestId: string,
    content: string,
    isComplete: boolean
  ): void {
    const messageElement = this.streamMessageElements.get(requestId);
    if (!messageElement) return;

    // 只有当content不为空时才更新文本内容
    if (content.trim()) {
      const textNode = messageElement.querySelector('.message-text');
      if (textNode) {
        textNode.textContent += content;
      }

      // 滚动到底部
      const chatContainer = this.getChatContentContainer(container);
      if (chatContainer) {
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }
    }

    // 如果响应完成，清除引用
    if (isComplete) {
      this.streamMessageElements.delete(requestId);
    }
  }

  /**
   * 清空聊天内容
   */
  public clearChatContent(container: HTMLElement, keepWelcome: boolean = true): void {
    const chatContainer = this.getChatContentContainer(container);
    if (!chatContainer) return;

    if (keepWelcome) {
      // 保留第一个欢迎消息
      const messages = chatContainer.querySelectorAll('.message');
      for (let i = 1; i < messages.length; i++) {
        const message = messages[i];
        if (message) {
          message.remove();
        }
      }
    } else {
      chatContainer.innerHTML = '';
    }

    // 清除流式消息引用
    this.streamMessageElements.clear();
  }

  /**
   * 设置返回按钮点击处理器
   */
  public setBackButtonHandler(container: HTMLElement, handler: () => void): void {
    const backButton = container.querySelector('#digital-human-back-button') as HTMLElement;
    if (backButton) {
      backButton.addEventListener('click', handler);
    }
  }

  /**
   * 销毁页面
   */
  public destroyPage(container: HTMLElement): void {
    if (container.parentElement) {
      container.parentElement.removeChild(container);
    }
    this.streamMessageElements.clear();
  }

  /**
   * 获取聊天内容容器
   */
  public getChatContentContainer(container: HTMLElement): HTMLElement | null {
    return container.querySelector('#digital-human-chat-content');
  }

  /**
   * 创建消息元素
   */
  private createMessageElement(message: ChatMessage): HTMLElement {
    const messageElement = document.createElement('div');
    messageElement.className = `message ${message.type}-message`;
    messageElement.setAttribute('data-message-id', message.id);

    if (message.requestId) {
      messageElement.setAttribute('data-request-id', message.requestId);
    }

    const isUser = message.type === 'user';
    const baseStyles = `
      padding: 12px 16px;
      border-radius: 18px;
      margin-bottom: 12px;
      max-width: 70%;
      box-shadow: 0 1px 2px rgba(0,0,0,0.1);
      line-height: 1.4;
      word-wrap: break-word;
    `;

    const typeStyles = isUser
      ? `
      background-color: #007bff;
      color: white;
      align-self: flex-end;
    `
      : `
      background-color: #f0f0f0;
      color: #333;
      align-self: flex-start;
    `;

    messageElement.style.cssText = baseStyles + typeStyles;

    // 添加图标和内容
    const icon = document.createElement('span');
    icon.className = 'message-icon';
    icon.style.cssText = `
      font-size: 12px;
      opacity: 0.8;
      margin-right: 6px;
    `;
    icon.textContent = isUser ? (message.isVoice ? '🎤 ' : '💬 ') : '🤖 ';

    const textElement = document.createElement('span');
    textElement.className = 'message-text';
    textElement.textContent = message.content;

    messageElement.appendChild(icon);
    messageElement.appendChild(textElement);

    return messageElement;
  }
}
