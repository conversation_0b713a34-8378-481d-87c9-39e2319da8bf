/**
 * 连接状态管理器
 * 统一管理所有连接的状态变化和事件
 */

// 简单的连接状态枚举
enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}
import { Logger, LogLevel } from '../utils/Logger';

import { EventBus } from './EventBus';

/**
 * 连接信息接口
 */
export interface ConnectionInfo {
  id: string;
  type: 'hkstt' | 'ai' | 'websocket' | 'http';
  url: string;
  state: ConnectionState;
  lastStateChange: number;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
  error?: Error;
}

/**
 * 连接状态变化事件
 */
export interface ConnectionStateChangeEvent {
  connectionId: string;
  previousState: ConnectionState;
  currentState: ConnectionState;
  timestamp: number;
  error?: Error | undefined;
}

/**
 * 连接状态管理器类
 * 提供统一的连接状态管理和监控
 */
export class ConnectionStateManager {
  private eventBus: EventBus;
  private logger: Logger;
  private connections: Map<string, ConnectionInfo> = new Map();

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.logger = Logger.getInstance({ level: LogLevel.INFO, prefix: 'ConnectionStateManager' });
  }

  /**
   * 注册连接
   * @param connectionInfo 连接信息
   */
  public registerConnection(connectionInfo: Omit<ConnectionInfo, 'lastStateChange'>): void {
    const fullInfo: ConnectionInfo = {
      ...connectionInfo,
      lastStateChange: Date.now(),
    };

    this.connections.set(connectionInfo.id, fullInfo);
    this.logger.debug(`注册连接: ${connectionInfo.id} (${connectionInfo.type})`);
  }

  /**
   * 更新连接状态
   * @param connectionId 连接ID
   * @param newState 新状态
   * @param error 错误信息（可选）
   */
  public updateConnectionState(
    connectionId: string,
    newState: ConnectionState,
    error?: Error
  ): void {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      this.logger.warn(`尝试更新未注册的连接状态: ${connectionId}`);
      return;
    }

    const previousState = connection.state;
    const timestamp = Date.now();

    // 更新连接信息
    connection.state = newState;
    connection.lastStateChange = timestamp;
    if (error) {
      connection.error = error;
    }

    // 更新重连计数
    if (newState === ConnectionState.CONNECTING && previousState !== ConnectionState.CONNECTING) {
      connection.reconnectAttempts++;
    } else if (newState === ConnectionState.CONNECTED) {
      connection.reconnectAttempts = 0;
      delete connection.error;
    }

    this.logger.debug(`连接状态变化: ${connectionId} ${previousState} -> ${newState}`);

    // 发送状态变化事件
    const stateChangeEvent: ConnectionStateChangeEvent = {
      connectionId,
      previousState,
      currentState: newState,
      timestamp,
      error,
    };

    this.eventBus.emit('connection:stateChange', stateChangeEvent);
    this.eventBus.emit(`connection:${connectionId}:stateChange`, stateChangeEvent);
  }

  /**
   * 获取连接状态
   * @param connectionId 连接ID
   * @returns 连接状态
   */
  public getConnectionState(connectionId: string): ConnectionState | null {
    const connection = this.connections.get(connectionId);
    return connection?.state ?? null;
  }

  /**
   * 获取连接信息
   * @param connectionId 连接ID
   * @returns 连接信息
   */
  public getConnectionInfo(connectionId: string): ConnectionInfo | null {
    const connection = this.connections.get(connectionId);
    return connection ? { ...connection } : null;
  }

  /**
   * 获取所有连接信息
   * @returns 所有连接信息
   */
  public getAllConnections(): ConnectionInfo[] {
    return Array.from(this.connections.values()).map(conn => ({ ...conn }));
  }

  /**
   * 获取指定类型的连接
   * @param type 连接类型
   * @returns 指定类型的连接信息数组
   */
  public getConnectionsByType(type: ConnectionInfo['type']): ConnectionInfo[] {
    return this.getAllConnections().filter(conn => conn.type === type);
  }

  /**
   * 检查连接是否已连接
   * @param connectionId 连接ID
   * @returns 是否已连接
   */
  public isConnected(connectionId: string): boolean {
    return this.getConnectionState(connectionId) === ConnectionState.CONNECTED;
  }

  /**
   * 检查连接是否可以重连
   * @param connectionId 连接ID
   * @returns 是否可以重连
   */
  public canReconnect(connectionId: string): boolean {
    const connection = this.connections.get(connectionId);
    if (!connection) return false;

    return (
      (connection.state === ConnectionState.DISCONNECTED ||
        connection.state === ConnectionState.ERROR) &&
      connection.reconnectAttempts < connection.maxReconnectAttempts
    );
  }

  /**
   * 注销连接
   * @param connectionId 连接ID
   */
  public unregisterConnection(connectionId: string): void {
    if (this.connections.delete(connectionId)) {
      this.logger.debug(`注销连接: ${connectionId}`);
      this.eventBus.emit('connection:unregistered', { connectionId });
    }
  }

  /**
   * 清理所有连接
   */
  public clear(): void {
    const connectionIds = Array.from(this.connections.keys());
    this.connections.clear();
    this.logger.debug('清理所有连接状态');

    connectionIds.forEach(id => {
      this.eventBus.emit('connection:unregistered', { connectionId: id });
    });
  }

  /**
   * 获取连接统计信息
   * @returns 连接统计信息
   */
  public getConnectionStats(): {
    total: number;
    connected: number;
    connecting: number;
    disconnected: number;
    error: number;
    reconnecting: number;
  } {
    const connections = this.getAllConnections();
    const stats = {
      total: connections.length,
      connected: 0,
      connecting: 0,
      disconnected: 0,
      error: 0,
      reconnecting: 0,
    };

    connections.forEach(conn => {
      switch (conn.state) {
        case ConnectionState.CONNECTED:
          stats.connected++;
          break;
        case ConnectionState.CONNECTING:
          stats.connecting++;
          break;
        case ConnectionState.DISCONNECTED:
          stats.disconnected++;
          break;
        case ConnectionState.ERROR:
          stats.error++;
          break;
        case ConnectionState.RECONNECTING:
          stats.reconnecting++;
          break;
      }
    });

    return stats;
  }
}
