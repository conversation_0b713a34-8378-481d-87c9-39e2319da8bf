import { EventBus } from '../core/EventBus';
import { Logger } from '../utils/Logger';

/**
 * 简化的WebSocket状态
 */
export enum SimpleWebSocketState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  FAILED = 'failed',
}

/**
 * 简化的WebSocket传输配置
 */
export interface SimpleWebSocketConfig {
  url: string;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  connectionTimeout?: number;
  /** 是否自动重连，默认true */
  autoReconnect?: boolean;
}

/**
 * 极简WebSocket传输层
 * 使用原生WebSocket，自实现重连逻辑
 */
export class SimpleWebSocketTransport {
  private config: Required<SimpleWebSocketConfig>;
  private eventBus: EventBus;
  private logger: Logger;
  private websocket: WebSocket | null = null;
  private state: SimpleWebSocketState = SimpleWebSocketState.DISCONNECTED;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private connectionTimer: NodeJS.Timeout | null = null;

  // 销毁标志
  private destroyed = false;

  constructor(config: SimpleWebSocketConfig, eventBus: EventBus) {
    this.config = {
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      connectionTimeout: 10000,
      autoReconnect: true,
      ...config,
    };
    this.eventBus = eventBus;
    this.logger = new Logger({ prefix: 'SimpleWebSocketTransport' });
  }

  /**
   * 连接WebSocket
   */
  public async connect(): Promise<void> {
    if (
      this.state === SimpleWebSocketState.CONNECTING ||
      this.state === SimpleWebSocketState.CONNECTED
    ) {
      this.logger.warn('WebSocket已连接或正在连接，跳过重复连接');
      return;
    }

    this.setState(SimpleWebSocketState.CONNECTING);
    this.logger.info('开始连接WebSocket', { url: this.config.url });

    return new Promise((resolve, reject) => {
      try {
        // 创建原生WebSocket
        this.websocket = new WebSocket(this.config.url);

        // 连接超时处理
        this.connectionTimer = setTimeout(() => {
          this.logger.error('WebSocket连接超时');
          this.cleanup();
          this.setState(SimpleWebSocketState.FAILED);
          reject(new Error('WebSocket连接超时'));
        }, this.config.connectionTimeout);

        // 连接成功
        this.websocket.onopen = () => {
          this.clearConnectionTimer();
          this.reconnectAttempts = 0;
          this.setState(SimpleWebSocketState.CONNECTED);
          this.logger.info('WebSocket连接成功');
          resolve();
        };

        // 接收消息
        this.websocket.onmessage = event => {
          this.handleMessage(event.data);
        };

        // 连接关闭
        this.websocket.onclose = event => {
          this.clearConnectionTimer();
          this.logger.warn('WebSocket连接关闭', {
            code: event.code,
            reason: event.reason,
            wasClean: event.wasClean,
          });

          if (this.state === SimpleWebSocketState.CONNECTED) {
            this.setState(SimpleWebSocketState.DISCONNECTED);
            this.scheduleReconnect();
          }
        };

        // 连接错误
        this.websocket.onerror = error => {
          this.clearConnectionTimer();
          this.logger.error('WebSocket连接错误', error);
          this.setState(SimpleWebSocketState.FAILED);
          reject(new Error('WebSocket连接失败'));
        };
      } catch (error) {
        this.clearConnectionTimer();
        this.logger.error('创建WebSocket失败', error);
        this.setState(SimpleWebSocketState.FAILED);
        reject(error);
      }
    });
  }

  /**
   * 发送消息
   */
  public send(data: string): void {
    if (!this.websocket) {
      throw new Error('WebSocket实例不存在');
    }

    if (this.websocket.readyState !== WebSocket.OPEN) {
      throw new Error(`WebSocket未就绪，当前状态: ${this.websocket.readyState}`);
    }

    try {
      this.websocket.send(data);
      this.logger.debug('发送WebSocket消息', { data });
    } catch (error) {
      this.logger.error('发送WebSocket消息失败', error);
      throw error;
    }
  }

  /**
   * 断开连接
   */
  public disconnect(): void {
    this.clearReconnectTimer();
    this.clearConnectionTimer();

    if (this.websocket) {
      this.logger.info('主动断开WebSocket连接');
      this.websocket.close(1000, 'Client disconnect');
      this.websocket = null;
    }

    this.setState(SimpleWebSocketState.DISCONNECTED);
  }

  /**
   * 检查是否已连接
   */
  public isConnected(): boolean {
    return (
      this.state === SimpleWebSocketState.CONNECTED &&
      this.websocket !== null &&
      this.websocket.readyState === WebSocket.OPEN
    );
  }

  /**
   * 获取当前状态
   */
  public getState(): SimpleWebSocketState {
    return this.state;
  }

  /**
   * 销毁传输层
   */
  public destroy(): void {
    this.destroyed = true;
    this.disconnect();
    this.logger.info('SimpleWebSocketTransport已销毁');
  }

  /**
   * 设置状态
   */
  private setState(newState: SimpleWebSocketState): void {
    const oldState = this.state;
    this.state = newState;

    if (oldState !== newState) {
      this.logger.info('WebSocket状态变化', { from: oldState, to: newState });
      this.eventBus.emit('transport:state-change', {
        state: newState,
        previousState: oldState,
      });
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(data: string): void {
    try {
      this.logger.debug('收到WebSocket消息', { data });
      // 保持与旧WebSocketTransport的兼容性
      this.eventBus.emit('message', { data });
    } catch (error) {
      this.logger.error('处理WebSocket消息失败', error);
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.destroyed || !this.config.autoReconnect) {
      this.logger.info('传输层已销毁或禁用自动重连，跳过重连');
      return;
    }

    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      this.logger.error('达到最大重连次数，停止重连', {
        attempts: this.reconnectAttempts,
        maxAttempts: this.config.maxReconnectAttempts,
      });
      this.setState(SimpleWebSocketState.FAILED);
      return;
    }

    this.reconnectAttempts++;
    this.logger.info('安排WebSocket重连', {
      attempt: this.reconnectAttempts,
      maxAttempts: this.config.maxReconnectAttempts,
      delay: this.config.reconnectInterval,
    });

    this.reconnectTimer = setTimeout(() => {
      if (this.destroyed) {
        this.logger.info('传输层已销毁，取消重连');
        return;
      }

      this.logger.info('开始WebSocket重连');
      this.connect().catch(error => {
        this.logger.error('WebSocket重连失败', error);
      });
    }, this.config.reconnectInterval);
  }

  /**
   * 清理连接定时器
   */
  private clearConnectionTimer(): void {
    if (this.connectionTimer) {
      clearTimeout(this.connectionTimer);
      this.connectionTimer = null;
    }
  }

  /**
   * 清理重连定时器
   */
  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    if (this.websocket) {
      this.websocket.onopen = null;
      this.websocket.onmessage = null;
      this.websocket.onclose = null;
      this.websocket.onerror = null;
      this.websocket = null;
    }
  }
}
