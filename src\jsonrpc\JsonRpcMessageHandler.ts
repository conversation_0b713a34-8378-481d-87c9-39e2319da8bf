/**
 * 轻量级JSON-RPC消息处理器
 * 只负责消息格式的解析和构建，不包含路由和状态管理
 */

import {
  JsonRpcRequest,
  JsonRpcResponse,
  JsonRpcErrorResponse,
  JsonRpcNotification,
  RequestContext,
  JsonRpcRequestWithContext,
} from '../types/jsonrpc';
import { generateUUID } from '../utils/helpers';

// 联合类型表示所有可能的JSON-RPC消息
type JsonRpcMessage = JsonRpcRequest | JsonRpcResponse | JsonRpcErrorResponse | JsonRpcNotification;

/**
 * 轻量级JSON-RPC消息处理器
 */
export class JsonRpcMessageHandler {
  /**
   * 解析JSON-RPC消息
   */
  public parseMessage(data: string): JsonRpcMessage | null {
    try {
      const message = JSON.parse(data);

      // 基本格式验证
      if (!message || typeof message !== 'object' || message.jsonrpc !== '2.0') {
        return null;
      }

      return message as JsonRpcMessage;
    } catch {
      return null;
    }
  }

  /**
   * 创建JSON-RPC请求（支持context）
   */
  public createRequest(
    method: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    params?: any /* JSON-RPC 标准参数 */,
    id?: string | number,
    context?: RequestContext
  ): JsonRpcRequest | JsonRpcRequestWithContext {
    const baseRequest = {
      jsonrpc: '2.0' as const,
      method,
      params,
      id: id || this.generateId(),
    };

    // 如果提供了context，返回扩展的请求格式
    if (context) {
      return {
        ...baseRequest,
        context,
      } as JsonRpcRequestWithContext;
    }

    return baseRequest;
  }

  /**
   * 创建带context的JSON-RPC请求（便捷方法）
   */
  public createRequestWithContext(
    method: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    params: any,
    context: RequestContext,
    id?: string | number
  ): JsonRpcRequestWithContext {
    return {
      jsonrpc: '2.0',
      method,
      params,
      context,
      id: id || this.generateId(),
    };
  }

  /**
   * 创建JSON-RPC响应
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public createResponse(result: any /* JSON-RPC 标准结果 */, id: string | number): JsonRpcResponse {
    return {
      jsonrpc: '2.0',
      result,
      id,
    };
  }

  /**
   * 创建JSON-RPC通知
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public createNotification(method: string, params?: any): JsonRpcNotification {
    return {
      jsonrpc: '2.0',
      method,
      params,
    };
  }

  /**
   * 创建JSON-RPC错误响应
   */
  public createError(
    code: number,
    message: string,
    id: string | number,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data?: any
  ): JsonRpcErrorResponse {
    return {
      jsonrpc: '2.0',
      error: {
        code,
        message,
        data,
      },
      id,
    };
  }

  /**
   * 判断是否为请求消息
   */
  public isRequest(message: JsonRpcMessage): message is JsonRpcRequest {
    return 'method' in message && 'id' in message;
  }

  /**
   * 判断是否为响应消息
   */
  public isResponse(message: JsonRpcMessage): message is JsonRpcResponse {
    return 'result' in message && 'id' in message;
  }

  /**
   * 判断是否为错误响应消息
   */
  public isErrorResponse(message: JsonRpcMessage): message is JsonRpcErrorResponse {
    return 'error' in message && 'id' in message;
  }

  /**
   * 判断是否为通知消息
   */
  public isNotification(message: JsonRpcMessage): message is JsonRpcNotification {
    return 'method' in message && !('id' in message);
  }

  /**
   * 判断是否为带context的请求消息
   */
  public isRequestWithContext(message: JsonRpcMessage): message is JsonRpcRequestWithContext {
    return this.isRequest(message) && 'context' in message;
  }

  /**
   * 生成请求ID（使用UUID）
   */
  private generateId(): string {
    return generateUUID();
  }

  /**
   * 序列化消息为JSON字符串
   */
  public stringify(message: JsonRpcMessage): string {
    return JSON.stringify(message);
  }

  /**
   * 验证消息格式
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public validateMessage(message: any): boolean {
    if (!message || typeof message !== 'object') {
      return false;
    }

    if (message.jsonrpc !== '2.0') {
      return false;
    }

    // 验证请求格式
    if ('method' in message && 'id' in message) {
      return (
        typeof message.method === 'string' &&
        (typeof message.id === 'string' || typeof message.id === 'number')
      );
    }

    // 验证响应格式
    if ('result' in message && 'id' in message) {
      return typeof message.id === 'string' || typeof message.id === 'number';
    }

    // 验证错误响应格式
    if ('error' in message && 'id' in message) {
      return (
        message.error &&
        typeof message.error.code === 'number' &&
        typeof message.error.message === 'string' &&
        (typeof message.id === 'string' || typeof message.id === 'number')
      );
    }

    // 验证通知格式
    if ('method' in message && !('id' in message)) {
      return typeof message.method === 'string';
    }

    return false;
  }
}
