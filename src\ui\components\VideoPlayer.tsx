/**
 * 数字人视频播放组件
 * 使用简化的数字人组件，支持WebP动画和与TTS的同步
 */

import React, { useRef, useImperativeHandle, forwardRef } from 'react';

import { WebSDK } from '../../core/WebSDK';

import { SimpleDigitalHumanReact, SimpleDigitalHumanReactRef } from './SimpleDigitalHumanReact';

interface VideoPlayerProps {
  sdk?: WebSDK;
  className?: string;
  style?: React.CSSProperties;
  microphoneMode?: 'button' | 'lip';
  layoutMode?: 'standard' | 'care';
}

export interface VideoPlayerRef {
  switchMicrophoneMode: (mode: 'button' | 'lip') => void;
  exitMicrophoneMode: () => void;
}

export const VideoPlayer = forwardRef<VideoPlayerRef, VideoPlayerProps>(
  ({ sdk, className, style, microphoneMode, layoutMode }, ref) => {
    const digitalHumanRef = useRef<SimpleDigitalHumanReactRef>(null);

    useImperativeHandle(
      ref,
      () => ({
        switchMicrophoneMode: (mode: 'button' | 'lip') => {
          digitalHumanRef.current?.switchMicrophoneMode(mode);
        },
        exitMicrophoneMode: () => {
          digitalHumanRef.current?.exitMicrophoneMode();
        },
      }),
      []
    );

    return (
      <SimpleDigitalHumanReact
        ref={digitalHumanRef}
        className={className || ''}
        style={style || {}}
        autoPlay={true}
        loop={true}
        microphoneMode={microphoneMode || 'button'}
        layoutMode={layoutMode || 'standard'}
        usageContext="greeting-page"
        sdk={sdk || undefined}
      />
    );
  }
);

VideoPlayer.displayName = 'VideoPlayer';
